# Hướng dẫn sử dụng EnhancedToast

`EnhancedToast` là component toast dùng chung cho toàn bộ dự án, gi<PERSON>p hiển thị các thông báo với giao diện nhất quán và chuyên nghiệp.

## Cách import

```tsx
import { enhancedToast } from "@/components/common/EnhancedToast";
```

## Các loại toast

`EnhancedToast` cung cấp 5 loại toast:

1. **Success**: Thông báo thành công
2. **Error**: Thông báo lỗi
3. **Warning**: Thông báo cảnh báo
4. **Info**: Thông báo thông tin
5. **Default**: Thông báo mặc định

## Cách sử dụng cơ bản

```tsx
// Thông báo thành công
enhancedToast.success("Thao tác thành công!");

// Thông báo lỗi
enhancedToast.error("Đã xảy ra lỗi!");

// Thông báo cảnh báo
enhancedToast.warning("Cảnh báo!");

// Thông báo thông tin
enhancedToast.info("Thông tin!");

// Thông báo mặc định
enhancedToast.default("Thông báo!");
```

## Tùy chỉnh nâng cao

Mỗi loại toast đều có thể được tùy chỉnh với các options:

```tsx
enhancedToast.success("Thao tác thành công!", {
  id: "unique-id", // ID duy nhất cho toast
  duration: 4000, // Thời gian hiển thị (ms)
  position: "top-right", // Vị trí hiển thị
  icon: <CheckCircle className="h-5 w-5" />, // Icon tùy chỉnh
  closeButton: true, // Hiển thị nút đóng
  action: { // Nút hành động
    label: "Hoàn tác",
    onClick: () => console.log("Hoàn tác")
  },
  cancel: { // Nút hủy
    label: "Hủy",
    onClick: () => console.log("Hủy")
  }
});
```

## Các options

| Option | Kiểu dữ liệu | Mô tả | Mặc định |
|--------|--------------|-------|----------|
| id | string | ID duy nhất cho toast | Tự động tạo |
| duration | number | Thời gian hiển thị (ms) | 4000 |
| position | "top-left" \| "top-right" \| "bottom-left" \| "bottom-right" \| "top-center" \| "bottom-center" | Vị trí hiển thị | "top-right" |
| icon | React.ReactNode | Icon tùy chỉnh | Tùy theo loại toast |
| closeButton | boolean | Hiển thị nút đóng | false |
| action | { label: string, onClick: () => void } | Nút hành động | undefined |
| cancel | { label: string, onClick: () => void } | Nút hủy | undefined |

## Ví dụ sử dụng với icon

```tsx
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react";
import { enhancedToast } from "@/components/common/EnhancedToast";

// Thông báo thành công với icon
enhancedToast.success("Thao tác thành công!", {
  icon: <CheckCircle className="h-5 w-5" />,
  duration: 4000,
  closeButton: true
});

// Thông báo lỗi với icon
enhancedToast.error("Đã xảy ra lỗi!", {
  icon: <XCircle className="h-5 w-5" />,
  duration: 5000,
  closeButton: true
});

// Thông báo cảnh báo với icon
enhancedToast.warning("Cảnh báo!", {
  icon: <AlertCircle className="h-5 w-5" />,
  duration: 5000,
  closeButton: true
});

// Thông báo thông tin với icon
enhancedToast.info("Thông tin!", {
  icon: <Info className="h-5 w-5" />,
  duration: 4000,
  closeButton: true
});
```

## Ví dụ sử dụng với promise

```tsx
enhancedToast.promise(
  fetch('/api/data'),
  {
    loading: 'Đang tải dữ liệu...',
    success: 'Tải dữ liệu thành công!',
    error: 'Không thể tải dữ liệu!'
  }
);
```

## Lưu ý

- Luôn sử dụng `enhancedToast` thay vì `toast` từ sonner để đảm bảo tính nhất quán trong toàn bộ dự án.
- Sử dụng các icon từ thư viện Lucide React để đảm bảo tính nhất quán về giao diện.
- Đối với các thông báo lỗi, nên hiển thị thông tin lỗi cụ thể nếu có.
