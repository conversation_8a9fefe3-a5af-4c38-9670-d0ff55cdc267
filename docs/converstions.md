# Conversations Service

Service quản lý các cuộc hội thoại trong hệ thống hỗ trợ khách hàng (CSKH). Cho phép tạo, quản lý và theo dõi các cuộc trò chuyện giữa khách hàng (buyer/shop) và đội ngũ hỗ trợ.

## 📋 Tính năng chính

- ✅ **Tạo cuộc hội thoại mới** giữa khách hàng và hỗ trợ
- ✅ **Phân loại khách hàng** (buyer/shop)
- ✅ **Quản lý trạng thái** (open/closed)
- ✅ **Gán nhân viên hỗ trợ** cho cuộc hội thoại
- ✅ **Quản lý tags** để phân loại chủ đề
- ✅ **Đóng/mở lại** cuộc hội thoại
- ✅ **Thống kê** cuộc hội thoại

## 🗂️ Schema

```javascript
{
  "_id": ObjectId,
  "customerId": ObjectId,           // ID khách hàng
  "customerType": "buyer" | "shop", // Loại khách hàng
  "supportId": ObjectId,            // ID nhân viên hỗ trợ (optional)
  "status": "open" | "closed",      // Trạng thái cuộc hội thoại
  "closedAt": Date,                 // Thời gian đóng (nếu có)
  "tags": ["urgent", "payment"],    // Tags phân loại
  "createdBy": ObjectId,            // Người tạo
  "updatedBy": ObjectId,            // Người cập nhật cuối
  "createdAt": Date,                // Thời gian tạo
  "updatedAt": Date                 // Thời gian cập nhật
}
```

## 🚀 API Endpoints

### 1. Tạo cuộc hội thoại mới
```http
POST /conversations
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerId": "60f1b2b3c4d5e6f7a8b9c0d1",
  "customerType": "buyer",
  "supportId": "60f1b2b3c4d5e6f7a8b9c0d2", // optional
  "tags": ["urgent", "payment"]              // optional
}
```

### 2. Lấy danh sách cuộc hội thoại
```http
GET /conversations?page=1&pageSize=10&populate=customer,support
Authorization: Bearer <token>
```

### 3. Lấy chi tiết cuộc hội thoại
```http
GET /conversations/:id
Authorization: Bearer <token>
```

### 4. Cập nhật cuộc hội thoại
```http
PUT /conversations/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "supportId": "60f1b2b3c4d5e6f7a8b9c0d2",
  "tags": ["resolved", "payment"]
}
```

### 5. Đóng cuộc hội thoại
```http
PUT /conversations/:id/close
Authorization: Bearer <token>
```

### 6. Mở lại cuộc hội thoại
```http
PUT /conversations/:id/reopen
Authorization: Bearer <token>
```

### 7. Gán nhân viên hỗ trợ
```http
PUT /conversations/:id/assign
Authorization: Bearer <token>
Content-Type: application/json

{
  "supportId": "60f1b2b3c4d5e6f7a8b9c0d2"
}
```

### 8. Thêm tags
```http
PUT /conversations/:id/tags/add
Authorization: Bearer <token>
Content-Type: application/json

{
  "tags": ["urgent", "billing"]
}
```

### 9. Thống kê cuộc hội thoại
```http
GET /conversations/stats
Authorization: Bearer <token>
```

## 📊 Populate Options

Service hỗ trợ populate thông tin liên quan:

- **customer**: Thông tin khách hàng (fullName, email, phone, avatarId)
- **support**: Thông tin nhân viên hỗ trợ (fullName, email, phone, avatarId)

## 🔔 Events

Service emit các events sau:

- `conversation.created`: Khi tạo cuộc hội thoại mới
- `conversation.updated`: Khi cập nhật cuộc hội thoại
- `conversation.closed`: Khi đóng cuộc hội thoại
- `conversation.reopened`: Khi mở lại cuộc hội thoại
- `conversation.support.assigned`: Khi gán nhân viên hỗ trợ
- `conversation.tags.added`: Khi thêm tags

## 🔍 Indexes

Để tối ưu performance, các indexes sau được tạo:

- `customerId + status`
- `supportId + status`
- `customerType + status`
- `tags`
- `customerId`
- `supportId`
- `status`

## 📝 Validation

- `customerId`: Required, phải tồn tại trong users collection
- `customerType`: Required, enum ["buyer", "shop"]
- `supportId`: Optional, nếu có phải tồn tại trong users collection
- `status`: Optional, enum ["open", "closed"], default "open"
- `tags`: Optional, array of strings

## 🔗 Dependencies

- **users service**: Để validate customerId và supportId
- **MongoDB**: Database chính
- **mongoose-paginate-v2**: Hỗ trợ pagination

## 💡 Sử dụng trong code

```javascript
// Import service constants
const { CONVERSATIONS_SERVICE } = require('./index');

// Tạo cuộc hội thoại mới
const conversation = await ctx.call(CONVERSATIONS_SERVICE.create, {
  customerId: "60f1b2b3c4d5e6f7a8b9c0d1",
  customerType: "buyer",
  tags: ["urgent"]
});

// Lấy cuộc hội thoại với populate
const conversation = await ctx.call(CONVERSATIONS_SERVICE.get, {
  id: "60f1b2b3c4d5e6f7a8b9c0d1",
  populate: ["customer", "support"]
});

// Đóng cuộc hội thoại
await ctx.call(CONVERSATIONS_SERVICE.close, {
  id: "60f1b2b3c4d5e6f7a8b9c0d1"
});
```

## 🏗️ Kiến trúc

Service sử dụng các mixins:
- **DbMongoose**: Kết nối MongoDB với Mongoose
- **BaseService**: Các action cơ bản (list, get, create, update, remove)
- **FunctionsCommon**: Các hàm tiện ích chung
