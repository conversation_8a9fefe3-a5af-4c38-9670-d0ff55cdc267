# Messages Service

Service quản lý tin nhắn trong các cuộc hội thoại hỗ trợ khách hàng. Cho phép gửi, nhận và quản lý tin nhắn giữa khách hàng (buyer/shop) và đội ngũ hỗ trợ.

## 📋 Tính năng chính

- ✅ **Gửi tin nhắn** trong cuộc hội thoại
- ✅ **Hỗ trợ file đính kèm** (attachments)
- ✅ **Phân loại người gửi** (buyer/shop/support)
- ✅ **Đánh dấu đã đọc** tin nhắn
- ✅ **Pagination** cho danh sách tin nhắn
- ✅ **Đếm tin nhắn chưa đọc**
- ✅ **Tự động xác định vai trò** người gửi

## 🗂️ Schema

```javascript
{
  "_id": ObjectId,
  "conversationId": ObjectId,              // ID cuộc hội thoại
  "senderId": ObjectId,                    // ID người gửi
  "senderRole": "buyer" | "shop" | "support", // Vai trò người gửi
  "text": "Đ<PERSON>n hàng của tôi sao chưa giao?", // Nội dung tin nhắn
  "attachments": [                         // File đính kèm
    {
      "fileId": ObjectId,
      "fileName": "invoice.pdf",
      "fileType": "application/pdf",
      "fileSize": 1024,
      "fileUrl": "/files/invoice.pdf"
    }
  ],
  "sentAt": Date,                          // Thời gian gửi
  "read": true,                            // Đã đọc hay chưa
  "readAt": Date,                          // Thời gian đọc
  "createdAt": Date,                       // Thời gian tạo
  "updatedAt": Date                        // Thời gian cập nhật
}
```

## 🚀 API Endpoints

### 1. Gửi tin nhắn mới
```http
POST /messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "conversationId": "60f1b2b3c4d5e6f7a8b9c0d1",
  "text": "Đơn hàng của tôi sao chưa giao?",
  "attachments": [                          // optional
    {
      "fileId": "60f1b2b3c4d5e6f7a8b9c0d2",
      "fileName": "invoice.pdf",
      "fileType": "application/pdf",
      "fileSize": 1024,
      "fileUrl": "/files/invoice.pdf"
    }
  ]
}
```

### 2. Lấy tin nhắn theo cuộc hội thoại
```http
GET /messages/conversation/:conversationId?page=1&pageSize=20
Authorization: Bearer <token>
```

### 3. Lấy chi tiết tin nhắn
```http
GET /messages/:id
Authorization: Bearer <token>
```

### 4. Đánh dấu tin nhắn đã đọc
```http
PUT /messages/:id/read
Authorization: Bearer <token>
```

### 5. Đánh dấu tất cả tin nhắn trong cuộc hội thoại đã đọc
```http
PUT /messages/conversation/:conversationId/read
Authorization: Bearer <token>
```

### 6. Đếm tin nhắn chưa đọc
```http
GET /messages/unread/count
Authorization: Bearer <token>
```

## 🎯 Tự động xác định vai trò (senderRole)

Service tự động xác định vai trò người gửi dựa trên:

1. **Nếu senderId = conversation.customerId** → senderRole = conversation.customerType (buyer/shop)
2. **Nếu senderId ≠ conversation.customerId** → senderRole = "support"

## 📊 Populate Options

Service hỗ trợ populate thông tin liên quan:

- **sender**: Thông tin người gửi (fullName, email, phone, avatarId)
- **conversation**: Thông tin cuộc hội thoại (customerId, customerType, supportId, status)

## 🔔 Events

Service emit các events sau:

- `message.created`: Khi gửi tin nhắn mới
- `message.read`: Khi đánh dấu tin nhắn đã đọc
- `conversation.messages.read`: Khi đánh dấu tất cả tin nhắn trong cuộc hội thoại đã đọc

## 🔍 Indexes

Để tối ưu performance, các indexes sau được tạo:

- `conversationId + sentAt` (desc)
- `senderId + sentAt` (desc)
- `conversationId + read`
- `senderRole + sentAt` (desc)
- `conversationId`
- `senderId`
- `senderRole`
- `sentAt`
- `read`

## 📝 Validation

- `conversationId`: Required, phải tồn tại trong conversations collection
- `text`: Required, minimum 1 character
- `attachments`: Optional, array of attachment objects
- `senderRole`: Auto-determined, enum ["buyer", "shop", "support"]

## 📄 File Attachments

Cấu trúc attachment object:

```javascript
{
  "fileId": ObjectId,        // ID file trong files collection
  "fileName": String,        // Tên file gốc
  "fileType": String,        // MIME type
  "fileSize": Number,        // Kích thước file (bytes)
  "fileUrl": String          // URL để download file
}
```

## 🔗 Dependencies

- **conversations service**: Để validate conversationId và xác định senderRole
- **users service**: Để populate thông tin sender
- **files service**: Để quản lý file attachments
- **MongoDB**: Database chính
- **mongoose-paginate-v2**: Hỗ trợ pagination

## 💡 Sử dụng trong code

```javascript
// Import service constants
const { MESSAGES_SERVICE } = require('./index');

// Gửi tin nhắn mới
const message = await ctx.call(MESSAGES_SERVICE.create, {
  conversationId: "60f1b2b3c4d5e6f7a8b9c0d1",
  text: "Xin chào, tôi cần hỗ trợ về đơn hàng",
  attachments: []
});

// Lấy tin nhắn theo cuộc hội thoại
const messages = await ctx.call(MESSAGES_SERVICE.getByConversation, {
  conversationId: "60f1b2b3c4d5e6f7a8b9c0d1",
  page: 1,
  pageSize: 20
});

// Đánh dấu tin nhắn đã đọc
await ctx.call(MESSAGES_SERVICE.markAsRead, {
  id: "60f1b2b3c4d5e6f7a8b9c0d2"
});

// Đếm tin nhắn chưa đọc
const { unreadCount } = await ctx.call(MESSAGES_SERVICE.getUnreadCount);
```

## 🏗️ Kiến trúc

Service sử dụng các mixins:
- **DbMongoose**: Kết nối MongoDB với Mongoose
- **BaseService**: Các action cơ bản (list, get, create, update, remove)
- **FunctionsCommon**: Các hàm tiện ích chung

## 🔄 Tích hợp với Conversations

Messages service tự động:
- Cập nhật `updatedAt` của conversation khi có tin nhắn mới
- Xác định `senderRole` dựa trên thông tin conversation
- Validate conversation tồn tại trước khi tạo tin nhắn
