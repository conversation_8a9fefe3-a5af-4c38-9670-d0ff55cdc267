# Hướng dẫn sử dụng Common Components

Tài liệu này mô tả các common component có sẵn trong dự án và cách sử dụng chúng.

## Mục lục

1. [DataTable - Bảng dữ liệu](#datatable)
2. [Pagination - <PERSON><PERSON> trang](#pagination)
3. [SearchBar - <PERSON><PERSON> tìm kiếm](#searchbar)
4. [PageHeader - Tiêu đề trang](#pageheader)
5. [ConfirmDeleteDialog - Dialog xác nhận xóa](#confirmdelete)
6. [StatusBadge - Badge trạng thái](#statusbadge)
7. [EmptyState - Trạng thái trống](#emptystate)
8. [ActionMenu - <PERSON>u hành động](#actionmenu)
9. [FilterBar - <PERSON>h lọc dữ liệu](#filterbar)
10. [FileUploader - Tải lên file](#fileuploader)
11. [InfoCard - Card thông tin](#infocard)
12. [FormDialog - Dialog form](#formdialog)

---

<a id="datatable"></a>
## 1. DataTable - Bảng dữ liệu

Component hiển thị dữ liệu dưới dạng bảng với phân trang tích hợp.

### Import

```tsx
import { DataTable } from "@/components/common/DataTable";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| data | T[] | Dữ liệu hiển thị trong bảng | - |
| columns | Column<T>[] | Định nghĩa các cột | - |
| loading | boolean | Trạng thái đang tải dữ liệu | false |
| paginationInfo | PaginationInfo | Thông tin phân trang | - |
| onPageChange | (page: number) => void | Callback khi thay đổi trang | - |
| itemName | string | Tên của các mục đang hiển thị | "mục" |
| emptyMessage | string | Thông báo khi không có dữ liệu | "Không có dữ liệu" |
| showPagination | boolean | Hiển thị phân trang | true |
| showFirstLastButtons | boolean | Hiển thị nút nhảy đến trang đầu/cuối | false |
| className | string | CSS class bổ sung | "" |
| onRowClick | (item: T, index: number) => void | Hàm xử lý khi click vào hàng | - |

### Ví dụ

```tsx
<DataTable
  data={administrators}
  loading={loading}
  paginationInfo={paginationInfo}
  onPageChange={handlePageChange}
  itemName="quản trị viên"
  showFirstLastButtons={true}
  emptyMessage="Không có dữ liệu quản trị viên"
  columns={[
    {
      header: "STT",
      cell: (_, index) => index + 1 + (paginationInfo.page - 1) * paginationInfo.limit,
      className: "w-[50px]"
    },
    {
      header: "Tên",
      accessorKey: "fullName"
    },
    {
      header: "Email",
      accessorKey: "email"
    },
    {
      header: "Trạng thái",
      cell: (admin) => (
        <StatusBadge variant={admin.status === "active" ? "success" : "default"}>
          {admin.status === "active" ? "Hoạt động" : "Không hoạt động"}
        </StatusBadge>
      )
    },
    {
      header: "Thao tác",
      className: "text-right",
      cell: (admin) => (
        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleEdit(admin)}
          >
            <Edit className="h-4 w-4"/>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDelete(admin)}
          >
            <Trash2 className="h-4 w-4 text-destructive"/>
          </Button>
        </div>
      )
    }
  ]}
/>
```

---

<a id="pagination"></a>
## 2. Pagination - Phân trang

Component hiển thị phân trang.

### Import

```tsx
import { Pagination } from "@/components/common/Pagination";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| paginationInfo | PaginationInfo | Thông tin phân trang | - |
| onPageChange | (page: number) => void | Callback khi thay đổi trang | - |
| itemName | string | Tên của các mục đang hiển thị | "mục" |
| showItemCount | boolean | Hiển thị thông tin về số lượng mục đang hiển thị | true |
| showFirstLastButtons | boolean | Hiển thị nút nhảy đến trang đầu/cuối | false |
| siblingCount | number | Số trang hiển thị xung quanh trang hiện tại | 1 |

### Ví dụ

```tsx
<Pagination
  paginationInfo={{
    total: 100,
    page: 1,
    limit: 10,
    totalPages: 10
  }}
  onPageChange={handlePageChange}
  itemName="quản trị viên"
  showFirstLastButtons={true}
/>
```

---

<a id="searchbar"></a>
## 3. SearchBar - Thanh tìm kiếm

Component hiển thị thanh tìm kiếm.

### Import

```tsx
import { SearchBar } from "@/components/common/SearchBar";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| value | string | Giá trị hiện tại của ô tìm kiếm | - |
| onChange | (value: string) => void | Callback khi giá trị thay đổi | - |
| placeholder | string | Placeholder cho ô tìm kiếm | "Tìm kiếm..." |
| useDebounce | boolean | Có sử dụng debounce không | true |
| debounceTime | number | Thời gian debounce (ms) | 300 |
| showClearButton | boolean | Có hiển thị nút xóa không | true |
| className | string | CSS class bổ sung | "" |
| showSearchButton | boolean | Có hiển thị nút tìm kiếm không | false |
| searchButtonText | string | Text của nút tìm kiếm | "Tìm kiếm" |

### Ví dụ

```tsx
<SearchBar 
  value={searchQuery} 
  onChange={(value) => setSearchQuery(value)} 
  placeholder="Tìm kiếm quản trị viên..."
  showClearButton={true}
  useDebounce={true}
  debounceTime={300}
/>
```

---

<a id="pageheader"></a>
## 4. PageHeader - Tiêu đề trang

Component hiển thị tiêu đề và mô tả của trang.

### Import

```tsx
import { PageHeader, PageHeaderAction } from "@/components/common/PageHeader";
```

### Props

#### PageHeader

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| title | string | Tiêu đề của trang | - |
| description | string | Mô tả của trang | - |
| actions | React.ReactNode | Các nút hành động | - |
| className | string | CSS class bổ sung | "" |
| children | React.ReactNode | Nội dung bổ sung | - |

#### PageHeaderAction

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| icon | React.ReactNode | Icon của nút | - |
| text | string | Text của nút | - |
| onClick | () => void | Callback khi click vào nút | - |
| variant | "default" \| "destructive" \| "outline" \| "secondary" \| "ghost" \| "link" | Variant của nút | "default" |
| className | string | CSS class bổ sung | "" |

### Ví dụ

```tsx
<PageHeader 
  title="Quản trị viên"
  description="Quản lý danh sách quản trị viên trong hệ thống"
  actions={
    <PageHeaderAction 
      icon={<Plus className="h-4 w-4" />}
      text="Thêm mới"
      onClick={handleAddNew}
    />
  }
/>
```

---

<a id="confirmdelete"></a>
## 5. ConfirmDeleteDialog - Dialog xác nhận xóa

Component hiển thị dialog xác nhận xóa.

### Import

```tsx
import { ConfirmDeleteDialog } from "@/components/common/ConfirmDeleteDialog";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| open | boolean | Trạng thái mở của dialog | - |
| onOpenChange | (open: boolean) => void | Callback khi thay đổi trạng thái mở | - |
| title | string | Tiêu đề của dialog | "Xác nhận xóa" |
| description | React.ReactNode | Nội dung mô tả của dialog | - |
| onConfirm | () => Promise<void> | Callback khi xác nhận xóa | - |
| confirmText | string | Text của nút xác nhận | "Xóa" |
| cancelText | string | Text của nút hủy | "Hủy" |
| deletingText | string | Text khi đang xóa | "Đang xóa..." |

### Ví dụ

```tsx
<ConfirmDeleteDialog
  open={deleteDialogOpen}
  onOpenChange={setDeleteDialogOpen}
  title="Xác nhận xóa quản trị viên"
  description={
    <>
      Bạn có chắc chắn muốn xóa quản trị viên {userToDelete?.fullName} ({userToDelete?.email}) không?
      <br />
      Hành động này không thể hoàn tác.
    </>
  }
  onConfirm={async () => {
    if (!userToDelete) return;
    await userService.deleteUser(userToDelete._id);
    fetchAdministrators();
  }}
/>
```

---

<a id="statusbadge"></a>
## 6. StatusBadge - Badge trạng thái

Component hiển thị trạng thái dưới dạng badge.

### Import

```tsx
import { StatusBadge } from "@/components/common/StatusBadge";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| children | React.ReactNode | Nội dung hiển thị | - |
| variant | "success" \| "warning" \| "error" \| "info" \| "default" \| "secondary" \| "outline" | Variant của badge | "default" |
| className | string | CSS class bổ sung | "" |
| size | "sm" \| "md" \| "lg" | Kích thước của badge | "md" |

### Ví dụ

```tsx
<StatusBadge variant="success">Hoạt động</StatusBadge>
<StatusBadge variant="error">Lỗi</StatusBadge>
<StatusBadge variant="warning">Cảnh báo</StatusBadge>
<StatusBadge variant="info">Thông tin</StatusBadge>
```

---

<a id="emptystate"></a>
## 7. EmptyState - Trạng thái trống

Component hiển thị trạng thái trống.

### Import

```tsx
import { EmptyState } from "@/components/common/EmptyState";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| title | string | Tiêu đề của trạng thái trống | - |
| description | string | Mô tả của trạng thái trống | - |
| icon | React.ReactNode | Icon hiển thị | - |
| actionText | string | Text của nút hành động | - |
| onAction | () => void | Callback khi click vào nút hành động | - |
| className | string | CSS class bổ sung | "" |
| children | React.ReactNode | Nội dung bổ sung | - |
| size | "sm" \| "default" \| "lg" | Kích thước của component | "default" |
| withBorder | boolean | Có hiển thị border không | false |

### Ví dụ

```tsx
<EmptyState
  title="Chưa có dữ liệu khách hàng"
  description="Bắt đầu thêm khách hàng mới vào hệ thống"
  icon={<Users className="h-6 w-6" />}
  actionText="Thêm khách hàng"
  onAction={handleAddCustomer}
  withBorder={true}
  size="lg"
/>
```

---

<a id="actionmenu"></a>
## 8. ActionMenu - Menu hành động

Component hiển thị menu hành động.

### Import

```tsx
import { ActionMenu } from "@/components/common/ActionMenu";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| actions | ActionItem[] | Danh sách các action | - |
| label | string | Label của menu | - |
| triggerButton | React.ReactNode | Nút trigger tùy chỉnh | - |
| triggerClassName | string | CSS class bổ sung cho trigger | "" |
| align | "start" \| "center" \| "end" | Vị trí của menu | "end" |
| showIcon | boolean | Có hiển thị icon mặc định không | true |

### Ví dụ

```tsx
<ActionMenu
  actions={[
    {
      label: "Chỉnh sửa",
      icon: <Edit className="h-4 w-4" />,
      onClick: () => handleEdit(item)
    },
    {
      label: "Xóa",
      icon: <Trash2 className="h-4 w-4" />,
      onClick: () => handleDelete(item),
      variant: "destructive"
    }
  ]}
/>
```

---

<a id="filterbar"></a>
## 9. FilterBar - Thanh lọc dữ liệu

Component hiển thị thanh lọc dữ liệu.

### Import

```tsx
import { FilterBar } from "@/components/common/FilterBar";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| filters | FilterItem[] | Danh sách các filter | - |
| onChange | (id: string, value: any) => void | Callback khi thay đổi giá trị filter | - |
| onReset | () => void | Callback khi reset tất cả filter | - |
| className | string | CSS class bổ sung | "" |
| showResetButton | boolean | Có hiển thị nút reset không | true |

### Ví dụ

```tsx
<FilterBar
  filters={[
    {
      id: "status",
      label: "Trạng thái",
      type: "select",
      value: filters.status,
      options: [
        { value: "active", label: "Hoạt động" },
        { value: "inactive", label: "Không hoạt động" }
      ]
    },
    {
      id: "role",
      label: "Vai trò",
      type: "select",
      value: filters.role,
      options: [
        { value: "admin", label: "Quản trị viên" },
        { value: "user", label: "Người dùng" }
      ]
    },
    {
      id: "createdAt",
      label: "Ngày tạo",
      type: "date",
      value: filters.createdAt
    }
  ]}
  onChange={handleFilterChange}
  onReset={handleResetFilters}
/>
```

---

<a id="fileuploader"></a>
## 10. FileUploader - Tải lên file

Component tải lên file.

### Import

```tsx
import { FileUploader } from "@/components/common/FileUploader";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| files | FileInfo[] | Danh sách file đã upload | - |
| onAddFiles | (files: File[]) => void | Callback khi thêm file | - |
| onRemoveFile | (fileId: string) => void | Callback khi xóa file | - |
| accept | string | Loại file được phép upload | "*" |
| multiple | boolean | Có cho phép upload nhiều file không | false |
| maxSize | number | Kích thước tối đa của file (bytes) | - |
| maxFiles | number | Số lượng file tối đa | - |
| className | string | CSS class bổ sung | "" |
| buttonText | string | Text của nút upload | "Tải lên file" |
| description | string | Mô tả | - |
| showPreview | boolean | Có hiển thị preview không | true |
| allowDragDrop | boolean | Có cho phép kéo thả không | true |
| disabled | boolean | Có disable không | false |

### Ví dụ

```tsx
<FileUploader
  files={files}
  onAddFiles={handleAddFiles}
  onRemoveFile={handleRemoveFile}
  accept="image/*"
  multiple={true}
  maxSize={5 * 1024 * 1024} // 5MB
  maxFiles={5}
  buttonText="Tải lên hình ảnh"
  description="Hỗ trợ các định dạng JPG, PNG, GIF"
/>
```

---

<a id="infocard"></a>
## 11. InfoCard - Card thông tin

Component hiển thị thông tin tổng quan.

### Import

```tsx
import { InfoCard } from "@/components/common/InfoCard";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| title | string | Tiêu đề của card | - |
| value | React.ReactNode | Giá trị chính hiển thị | - |
| icon | React.ReactNode | Icon hiển thị | - |
| iconColor | string | Màu của icon | - |
| footer | React.ReactNode | Nội dung footer | - |
| description | React.ReactNode | Mô tả | - |
| className | string | CSS class bổ sung | "" |
| loading | boolean | Trạng thái loading | false |
| onClick | () => void | Callback khi click vào card | - |
| withBorder | boolean | Có hiển thị border không | true |
| withShadow | boolean | Có hiển thị shadow không | false |
| withHoverEffect | boolean | Có hiển thị hover effect không | false |

### Ví dụ

```tsx
<InfoCard
  title="Tổng số khách hàng"
  value="1,234"
  icon={<Users className="h-4 w-4" />}
  description="Tăng 12% so với tháng trước"
  withBorder={true}
  withHoverEffect={true}
/>
```

---

<a id="formdialog"></a>
## 12. FormDialog - Dialog form

Component hiển thị dialog form.

### Import

```tsx
import { FormDialog } from "@/components/common/FormDialog";
```

### Props

| Prop | Kiểu dữ liệu | Mô tả | Mặc định |
|------|--------------|-------|----------|
| open | boolean | Trạng thái mở của dialog | - |
| onOpenChange | (open: boolean) => void | Callback khi thay đổi trạng thái mở | - |
| title | string | Tiêu đề của dialog | - |
| description | string | Mô tả của dialog | - |
| children | React.ReactNode | Nội dung form | - |
| isSubmitting | boolean | Đang submit form | false |
| submitText | string | Text của nút submit | "Lưu" |
| cancelText | string | Text của nút hủy | "Hủy" |
| submittingText | string | Text khi đang submit | "Đang lưu..." |
| onSubmit | () => void | Callback khi submit form | - |
| maxWidth | "sm" \| "md" \| "lg" \| "xl" \| "2xl" \| "3xl" \| "4xl" \| "5xl" \| "full" | Kích thước tối đa của dialog | "md" |
| showFooter | boolean | Có hiển thị footer không | true |
| showCancelButton | boolean | Có hiển thị nút hủy không | true |
| showSubmitButton | boolean | Có hiển thị nút submit không | true |
| footerContent | React.ReactNode | Nội dung footer tùy chỉnh | - |

### Ví dụ

```tsx
<FormDialog
  open={dialogOpen}
  onOpenChange={setDialogOpen}
  title="Thêm mới quản trị viên"
  description="Nhập thông tin quản trị viên mới"
  isSubmitting={isSubmitting}
  onSubmit={handleSubmit}
>
  <div className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="name">Họ và tên</Label>
      <Input id="name" value={name} onChange={(e) => setName(e.target.value)} />
    </div>
    <div className="space-y-2">
      <Label htmlFor="email">Email</Label>
      <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
    </div>
  </div>
</FormDialog>
```
