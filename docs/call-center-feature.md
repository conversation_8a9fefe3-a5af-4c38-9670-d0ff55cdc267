# Call Center Feature Documentation

## Overview

The Call Center feature in the GHVN-CSKH-Admin application provides a web-based interface for handling voice calls using WebRTC and SIP (Session Initiation Protocol) technology. This module enables customer service representatives to make and receive calls directly from their web browser, eliminating the need for dedicated phone hardware.

## Architecture

The Call Center feature is built using the following components:

### 1. SIP.js Library
- Provides WebRTC-based SIP implementation
- Handles SIP signaling and media negotiation
- Manages SIP sessions and registration

### 2. React Components
- `CallCenterPage`: Main container component for the call center interface
- `CallInterface`: Handles the call UI and user interactions

### 3. Custom Hooks
- `useSipCall`: React hook that encapsulates SIP functionality

### 4. Services
- `sipService`: Singleton service that manages SIP connections and call state

## Configuration

The SIP configuration is defined in `src/features/call-center/config/sipConfig.ts`:

```typescript
export const SIP_CONFIG = {
    // SIP Server
    SERVER_URL: '***************:5090',
    WEBSOCKET_URL: 'ws://***************:8088/ws',

    // Default account
    DEFAULT_USER: 'e1',
    DEFAULT_PASSWORD: 'Xproz2025',

    // Extensions
    EXTENSIONS: {
        E1: '1',
        E2: '2',
        DEMO: '6000',
        JACK: 'jack',
        RECORDING: '6001'
    },

    // Other configuration
    MAIN_SERVER: '**************:5060',
    BACKUP_SERVER: '***************:5060',
    SIGNALING_PORT: '5060',
    RTP_PORT_RANGE: '10000-65535',
    FROM_PREFIX: '09',
    TO_PREFIX: '9',

    // STUN servers for WebRTC
    ICE_SERVERS: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
    ]
};
```

## User Flow

### 1. SIP Account Configuration
- User enters SIP credentials (username and password)
- System attempts to connect to the SIP server
- On successful connection, the call interface is displayed

### 2. Making Outgoing Calls
- User enters the destination number or extension
- User clicks "Call" to initiate a call with microphone access
- Alternatively, user can click "Listen Only" to make a call without microphone access
- Call status is displayed (connecting, connected, etc.)
- Call duration is tracked
- User can control the call (mute/unmute, record, hang up)

### 3. Receiving Incoming Calls
- When a call is received, an incoming call notification is displayed
- User can answer with microphone, answer in listen-only mode, or reject the call
- On answer, the call is connected and the call interface is updated
- Call controls become available

### 4. Call Controls
- **Mute/Unmute**: Toggle microphone status
- **Record**: Start/stop call recording
- **Hang Up**: End the current call

## Technical Implementation

### SIP Service

The `sipService` is a singleton that manages the SIP connection and call state:

- **Initialization**: Connects to the SIP server and registers the user
- **Call Management**: Handles making, answering, and ending calls
- **Media Control**: Manages audio streams and recording
- **State Management**: Tracks and updates call state

### Call States

The call can be in one of the following states:

```typescript
export enum CallStatus {
    IDLE = 'idle',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    DISCONNECTED = 'disconnected',
    INCOMING = 'incoming',
    OUTGOING = 'outgoing',
    ERROR = 'error'
}
```

### Error Handling

The system handles various error scenarios:

- **Connection Errors**: Problems connecting to the SIP server
- **Authentication Errors**: Invalid credentials
- **Media Access Errors**: Issues accessing the microphone
- **Call Errors**: Problems during call setup or termination

Detailed error messages are provided to help users troubleshoot issues.

## WebRTC Integration

The call center uses WebRTC for media handling:

- **getUserMedia**: Requests access to the user's microphone
- **RTCPeerConnection**: Establishes peer-to-peer connections
- **STUN Servers**: Helps with NAT traversal for establishing connections

## Listen-Only Mode

A unique feature is the ability to make or answer calls in "listen-only" mode:

- Allows users without a microphone to participate in calls
- Useful in environments where speaking is not possible
- Implemented by skipping media access during call setup

## Recording Functionality

The call recording feature:

- Allows users to record important calls
- Recording state is tracked in the UI
- In a production environment, this would typically be implemented server-side

## Accessibility Considerations

The interface includes:

- Clear status indicators
- Descriptive button labels
- Error messages with troubleshooting suggestions
- Keyboard-accessible controls

## Security Considerations

- SIP credentials should be securely stored
- WebSocket connections should use WSS (secure WebSockets) in production
- Media permissions are explicitly requested from the user

## Future Enhancements

Potential improvements for the call center feature:

1. Call transfer functionality
2. Conference call support
3. Call queue management
4. Integration with CRM systems
5. Call analytics and reporting
6. Enhanced recording with transcription
7. Video call support

## Troubleshooting

Common issues and solutions:

1. **Cannot connect to SIP server**
   - Check network connectivity
   - Verify SIP server is running
   - Ensure firewall allows WebSocket connections

2. **Authentication failures**
   - Verify username and password
   - Check if account is active on the SIP server

3. **No audio**
   - Check microphone permissions
   - Verify audio device is working
   - Check if browser supports WebRTC

4. **Call setup failures**
   - Check STUN server connectivity
   - Verify SIP configuration
   - Check for network restrictions
