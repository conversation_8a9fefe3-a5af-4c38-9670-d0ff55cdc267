# Base Service Documentation - React TypeScript Implementation

## Tổng quan

Base Service là một lớp trừu tượng cung cấp các phương thức CRUD cơ bản cho việc giao tiếp với API backend. <PERSON><PERSON> thiết kế để tái sử dụng cho tất cả các service khác trong ứng dụng React.

## Cấu trúc hiện tại (JavaScript)

<augment_code_snippet path="src/app/services/Base/index.js" mode="EXCERPT">
````javascript
import axios from "axios";
import { genPopulateParam, genQueryParam, genSearchFieldParam } from "@src/common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@src/common/dataConverter";

export function createBase(api, data, populateOpts = [], loading = true, toastError = false, params) {
  const config = { loading, toastError, params };
  const populateParams = genPopulateParam(populateOpts);
  return axios.post(`${api}` + "?" + populateParams, (data), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}
````
</augment_code_snippet>

## Thiết kế cho React TypeScript

### 1. Định nghĩa Types và Interfaces

```typescript
// types/api.types.ts
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  limit?: number;
}

export interface QueryParams {
  [key: string]: any;
  sort?: string;
  limit?: number;
}

export interface RequestConfig {
  loading?: boolean;
  toastError?: boolean;
  hideNoti?: boolean;
  params?: any;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
```

### 2. Base Service Class

```typescript
// services/BaseService.ts
import axios, { AxiosResponse } from 'axios';
import {
  ApiResponse,
  PaginationParams,
  QueryParams,
  RequestConfig,
  PaginatedResponse
} from '../types/api.types';
import {
  genPopulateParam,
  genQueryParam,
  genSearchFieldParam,
  convertSnakeCaseToCamelCase
} from '../utils';

export class BaseService {

  /**
   * Nối các tham số thành chuỗi query string
   */
  protected joinParams(arrParam: (string | null | undefined)[]): string {
    if (!Array.isArray(arrParam)) return "";
    return arrParam.filter(x => !!x).join("&");
  }

  /**
   * Tạo mới một resource
   */
  async create<T = any>(
    api: string,
    data: any,
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false,
    params?: any
  ): Promise<T | null> {
    try {
      const config: RequestConfig = { loading, toastError, params };
      const populateParams = genPopulateParam(populateOpts);

      const response: AxiosResponse<ApiResponse<T>> = await axios.post(
        `${api}?${populateParams}`,
        data,
        config
      );

      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data.data);
      }
      return null;
    } catch (error) {
      console.error('Create error:', error);
      return null;
    }
  }

  /**
   * Lấy một resource theo query
   */
  async get<T = any>(
    api: string,
    query: QueryParams = {},
    populateOpts: string[] = [],
    loading: boolean = true,
    searchFields?: string[]
  ): Promise<T | null> {
    try {
      const config: RequestConfig = { loading };

      const arrParams = [
        genQueryParam(query),
        genPopulateParam(populateOpts),
        genSearchFieldParam(searchFields),
      ];

      const response: AxiosResponse<ApiResponse<T>> = await axios.get(
        `${api}?${this.joinParams(arrParams)}`,
        config
      );

      if (response.status === 200) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('Get error:', error);
      return null;
    }
  }

  /**
   * Lấy tất cả resources
   */
  async getAll<T = any>(
    api: string,
    query: QueryParams = {},
    populateOpts: string[] = [],
    loading: boolean = true,
    searchFields?: string[]
  ): Promise<T[] | null> {
    try {
      const config: RequestConfig = { loading };

      const arrParams = [
        genQueryParam(query),
        genPopulateParam(populateOpts),
        genSearchFieldParam(searchFields),
      ];

      const response: AxiosResponse<ApiResponse<T[]>> = await axios.get(
        `${api}/findAll?${this.joinParams(arrParams)}`,
        config
      );

      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data.data);
      }
      return null;
    } catch (error) {
      console.error('GetAll error:', error);
      return null;
    }
  }

  /**
   * Lấy resources với phân trang
   */
  async getAllPagination<T = any>(
    api: string,
    paging: PaginationParams,
    query: QueryParams = {},
    searchFields: string[] = [],
    populateOpts: string[] = [],
    loading: boolean = true
  ): Promise<PaginatedResponse<T> | null> {
    try {
      const currentPage = paging?.page || 1;
      const pageSize = paging?.pageSize || paging?.limit || 10;

      const config: RequestConfig = { loading };

      const arrParams = [
        `page=${currentPage}`,
        `pageSize=${pageSize}`,
        genQueryParam(query),
        genPopulateParam(populateOpts),
        genSearchFieldParam(searchFields),
      ];

      const response: AxiosResponse<ApiResponse<PaginatedResponse<T>>> = await axios.get(
        `${api}?${this.joinParams(arrParams)}`,
        config
      );

      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data.data);
      }
      return null;
    } catch (error) {
      console.error('GetAllPagination error:', error);
      return null;
    }
  }

  /**
   * Lấy chi tiết một resource theo ID
   */
  async getDetail<T = any>(
    api: string,
    id: string,
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<T | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const populateParams = genPopulateParam(populateOpts);

      const response: AxiosResponse<ApiResponse<T>> = await axios.get(
        `${api.replace('{0}', id)}?${populateParams}`,
        config
      );

      if (response.status === 200) {
        return response.data.data;
      }
      return null;
    } catch (error) {
      console.error('GetDetail error:', error);
      return null;
    }
  }

  /**
   * Cập nhật một resource
   */
  async update<T = any>(
    api: string,
    data: { _id: string; [key: string]: any },
    populateOpts: string[] = [],
    loading: boolean = true,
    showNoti: boolean = false,
    params?: any
  ): Promise<T | null> {
    try {
      const { _id, ...updateData } = data;
      const config: RequestConfig = { loading, hideNoti: !showNoti, params };
      const populateParams = genPopulateParam(populateOpts);

      const response: AxiosResponse<ApiResponse<T>> = await axios.put(
        `${api.replace('{0}', _id)}?${populateParams}`,
        updateData,
        config
      );

      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data.data);
      }
      return null;
    } catch (error) {
      console.error('Update error:', error);
      return null;
    }
  }

  /**
   * Xóa một resource
   */
  async delete<T = any>(
    api: string,
    id: string,
    loading: boolean = true,
    showNoti: boolean = false
  ): Promise<T | null> {
    try {
      const config: RequestConfig = { loading, hideNoti: !showNoti };

      const response: AxiosResponse<ApiResponse<T>> = await axios.delete(
        api.replace('{0}', id),
        config
      );

      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data.data);
      }
      return null;
    } catch (error) {
      console.error('Delete error:', error);
      return null;
    }
  }
}

// Export singleton instance
export const baseService = new BaseService();
```

### 3. Utility Functions

```typescript
// utils/apiHelpers.ts
export function genQueryParam(queryObj: Record<string, any> = {}): string {
  if (typeof queryObj !== "object" || !Object.keys(queryObj).length) return "";

  const queryArr: string[] = [];
  const queryObjCopy = { ...queryObj };

  if (queryObjCopy.hasOwnProperty("sort")) {
    queryArr.push(`sort=${queryObjCopy.sort}`);
    delete queryObjCopy.sort;
  }

  if (queryObjCopy.hasOwnProperty("limit")) {
    queryArr.push(`limit=${queryObjCopy.limit}`);
    delete queryObjCopy.limit;
  }

  Object.entries(queryObjCopy).forEach(([key, value]) => {
    if (!value) {
      delete queryObjCopy[key];
    } else {
      queryObjCopy[key] = encodeURIComponent(value);
    }
  });

  if (Object.keys(queryObjCopy).length) {
    queryArr.push(`query=${JSON.stringify(queryObjCopy)}`);
  }

  return queryArr.reverse().join("&");
}

export function genPopulateParam(populateOpts: string[] = []): string {
  if (!populateOpts?.length) return "";
  return `populate=${populateOpts.join(",")}`;
}

export function genSearchFieldParam(searchFields: string[] = []): string {
  if (!searchFields?.length) return "";
  return `searchFields=${searchFields.join(",")}`;
}
```

### 4. Cách sử dụng trong Service cụ thể

```typescript
// services/ProjectService.ts
import { baseService } from './BaseService';
import { API } from '../constants/api';

export interface Project {
  _id: string;
  name: string;
  description?: string;
  ownerId: string;
  imageId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProjectData {
  name: string;
  description?: string;
  ownerId: string;
}

export class ProjectService extends BaseService {

  async createProject(data: CreateProjectData): Promise<Project | null> {
    return this.create<Project>(API.PROJECT, data);
  }

  async getAllProjects(query: any = {}, loading: boolean = true): Promise<Project[] | null> {
    return this.getAll<Project>(API.PROJECT, query, ["ownerId", "imageId"], loading);
  }

  async getProjectDetail(id: string): Promise<Project | null> {
    return this.getDetail<Project>(API.PROJECT_ID, id, ["ownerId", "imageId"]);
  }

  async updateProject(data: Project): Promise<Project | null> {
    return this.update<Project>(API.PROJECT_ID, data, [], true, true);
  }

  async deleteProject(id: string): Promise<any> {
    return this.delete(API.PROJECT_ID, id, true, true);
  }
}

export const projectService = new ProjectService();
```

### 5. Axios Configuration cho TypeScript

```typescript
// config/axios.config.ts
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { store } from '../store';
import { toggleLoading } from '../store/slices/appSlice';
import { toast } from '../components/ToastProvider';

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  loading?: boolean;
  toastError?: boolean;
  hideNoti?: boolean;
}

let countApiRequest = 0;
let countApiResponse = 0;

// Request interceptor
axios.interceptors.request.use(
  (config: CustomAxiosRequestConfig) => {
    // Set default loading
    if (!config.hasOwnProperty("loading")) {
      config.loading = true;
    }

    if (config.loading) {
      countApiRequest++;
    }

    const { app } = store.getState();
    const language = localStorage.getItem("i18nextLng") || "en";

    config.headers = {
      ...config.headers,
      "accept-language": language,
      "i18nextlng": language,
    };

    if (!app.isLoading && countApiRequest !== countApiResponse) {
      store.dispatch(toggleLoading(true));
    }

    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Response interceptor
axios.interceptors.response.use(
  (response: AxiosResponse) => {
    const config = response.config as CustomAxiosRequestConfig;

    if (config.loading) {
      countApiResponse++;
    }

    if (countApiRequest === countApiResponse) {
      store.dispatch(toggleLoading(false));
    }

    return response;
  },
  (error: AxiosError) => {
    const config = error.config as CustomAxiosRequestConfig;

    if (config?.loading) {
      countApiResponse++;
    }

    if (countApiRequest === countApiResponse) {
      store.dispatch(toggleLoading(false));
    }

    // Handle error notifications
    if (error.response && !config?.hideNoti) {
      const message = error.response.data?.message || "An error occurred";
      toast.error(message);
    }

    return Promise.reject(error);
  }
);

export default axios;
```

### 6. Hook sử dụng Service

```typescript
// hooks/useProjectService.ts
import { useState, useCallback } from 'react';
import { projectService, Project, CreateProjectData } from '../services/ProjectService';

export const useProjectService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createProject = useCallback(async (data: CreateProjectData): Promise<Project | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await projectService.createProject(data);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getAllProjects = useCallback(async (query: any = {}): Promise<Project[] | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await projectService.getAllProjects(query);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    createProject,
    getAllProjects,
  };
};
```

### 7. Component sử dụng

```typescript
// components/ProjectList.tsx
import React, { useEffect, useState } from 'react';
import { useProjectService } from '../hooks/useProjectService';
import { Project } from '../services/ProjectService';

const ProjectList: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const { loading, error, getAllProjects } = useProjectService();

  useEffect(() => {
    const fetchProjects = async () => {
      const result = await getAllProjects();
      if (result) {
        setProjects(result);
      }
    };

    fetchProjects();
  }, [getAllProjects]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Projects</h2>
      {projects.map(project => (
        <div key={project._id}>
          <h3>{project.name}</h3>
          <p>{project.description}</p>
        </div>
      ))}
    </div>
  );
};

export default ProjectList;
```

## Lợi ích của thiết kế TypeScript

1. **Type Safety**: Đảm bảo kiểu dữ liệu chính xác
2. **IntelliSense**: Hỗ trợ autocomplete tốt hơn
3. **Error Detection**: Phát hiện lỗi sớm trong quá trình development
4. **Maintainability**: Dễ bảo trì và refactor code
5. **Documentation**: Types serve as documentation

## Các pattern được áp dụng

1. **Singleton Pattern**: BaseService instance duy nhất
2. **Template Method Pattern**: Base class định nghĩa skeleton, subclass implement details
3. **Factory Pattern**: Service factory cho các loại service khác nhau
4. **Observer Pattern**: Axios interceptors để handle loading state
5. **Strategy Pattern**: Khác nhau strategies cho error handling

## Best Practices

1. Luôn định nghĩa interfaces cho request/response data
2. Sử dụng generic types để tái sử dụng
3. Handle errors một cách consistent
4. Implement proper loading states
5. Use custom hooks để encapsulate service logic
6. Implement proper TypeScript strict mode
7. Use proper error boundaries trong React components
```
