# Tài liệu tính năng Trung tâm cuộc gọi (Call Center)

## Tổng quan

Tính năng Trung tâm cuộc gọi trong ứng dụng GHVN-CSKH-Admin cung cấp giao diện web để xử lý cuộc gọi thoại sử dụng công nghệ WebRTC và SIP (Session Initiation Protocol). <PERSON><PERSON><PERSON> này cho phép nhân viên dịch vụ khách hàng thực hiện và nhận cuộc gọi trực tiếp từ trình duyệt web, loại bỏ nhu cầu sử dụng phần cứng điện thoại chuyên dụng.

## Kiến trúc

Tính năng Trung tâm cuộc gọi được xây dựng sử dụng các thành phần sau:

### 1. Thư viện SIP.js
- <PERSON><PERSON> cấp triển khai SIP dựa trên WebRTC
- Xử lý báo hiệu SIP và đàm phán media
- Quản lý phiên SIP và đăng ký

### 2. React Components
- `CallCenterPage`: Component container chính cho giao diện trung tâm cuộc gọi
- `CallInterface`: Xử lý giao diện người dùng cuộc gọi và tương tác người dùng

### 3. Custom Hooks
- `useSipCall`: React hook đóng gói chức năng SIP

### 4. Services
- `sipService`: Service singleton quản lý kết nối SIP và trạng thái cuộc gọi

## Cấu trúc thư mục

```
src/features/call-center/
├── components/
│   └── CallInterface.tsx     # Giao diện người dùng cho cuộc gọi
├── config/
│   └── sipConfig.ts          # Cấu hình SIP
├── hooks/
│   └── useSipCall.ts         # Custom hook để sử dụng dịch vụ SIP
├── pages/
│   └── CallCenterPage.tsx    # Trang chính của trung tâm cuộc gọi
├── services/
│   └── sipService.ts         # Dịch vụ xử lý kết nối SIP
└── index.ts                  # Exports các thành phần chính
```

## Cấu hình

Cấu hình SIP được định nghĩa trong `src/features/call-center/config/sipConfig.ts`:

```typescript
export const SIP_CONFIG = {
    // SIP Server
    SERVER_URL: '***************:5090',
    WEBSOCKET_URL: 'ws://***************:8088/ws',

    // Tài khoản mặc định
    DEFAULT_USER: 'e1',
    DEFAULT_PASSWORD: 'Xproz2025',

    // Extensions
    EXTENSIONS: {
        E1: '1',
        E2: '2',
        DEMO: '6000',
        JACK: 'jack',
        RECORDING: '6001'
    },

    // Cấu hình khác
    MAIN_SERVER: '**************:5060',
    BACKUP_SERVER: '***************:5060',
    SIGNALING_PORT: '5060',
    RTP_PORT_RANGE: '10000-65535',
    FROM_PREFIX: '09',
    TO_PREFIX: '9',

    // Cấu hình STUN servers cho WebRTC
    ICE_SERVERS: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
    ]
};
```

## Các thành phần chính

### 1. CallCenterPage

Đây là trang chính của tính năng trung tâm cuộc gọi. Nó cho phép người dùng cấu hình thông tin đăng nhập SIP và hiển thị giao diện cuộc gọi.

```typescript
const CallCenterPage: React.FC = () => {
    const [username, setUsername] = useState(SIP_CONFIG.DEFAULT_USER);
    const [password, setPassword] = useState(SIP_CONFIG.DEFAULT_PASSWORD);
    const [isConfigured, setIsConfigured] = useState(false);
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsConfigured(true);
    };
    
    return (
        <div className="app-content content">
            {/* ... */}
            <div className="content-body">
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">
                                {!isConfigured ? (
                                    <div className="row">
                                        <div className="col-md-6 offset-md-3">
                                            <h4>Cấu hình SIP</h4>
                                            <form onSubmit={handleSubmit}>
                                                {/* Form cấu hình */}
                                            </form>
                                        </div>
                                    </div>
                                ) : (
                                    <CallInterface username={username} password={password} />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
```

### 2. CallInterface

Component này xử lý giao diện người dùng cho cuộc gọi, bao gồm:
- Hiển thị trạng thái cuộc gọi
- Nhập số điện thoại để gọi
- Các nút điều khiển cuộc gọi (gọi, trả lời, từ chối, kết thúc)
- Tắt/bật microphone
- Ghi âm cuộc gọi

```typescript
const CallInterface: React.FC<CallInterfaceProps> = ({
    username = SIP_CONFIG.DEFAULT_USER,
    password = SIP_CONFIG.DEFAULT_PASSWORD
}) => {
    const [destination, setDestination] = useState('');
    const [callDuration, setCallDuration] = useState(0);
    const [timerInterval, setTimerInterval] = useState<NodeJS.Timeout | null>(null);

    const {
        callState,
        isInitialized,
        isMuted,
        initialize,
        makeCall,
        answerCall,
        rejectCall,
        hangupCall,
        startRecording,
        stopRecording,
        toggleMute
    } = useSipCall({ autoConnect: false });

    // ... các hàm xử lý và hiệu ứng

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">Giao diện gọi điện</h4>
                <div className="d-flex align-items-center">
                    {renderCallStatus()}
                    {/* Hiển thị trạng thái kết nối */}
                </div>
            </div>

            <div className="card-body">
                {/* Thông tin cuộc gọi */}
                {/* Form nhập số điện thoại */}
                {/* Nút điều khiển cuộc gọi đến */}
                {/* Nút điều khiển cuộc gọi đang diễn ra */}
                {/* Thông báo lỗi */}
            </div>
        </div>
    );
};
```

### 3. useSipCall Hook

Hook này đóng gói tất cả chức năng SIP và cung cấp một API đơn giản để sử dụng trong các component React:

```typescript
export const useSipCall = ({
    autoConnect = true,
    username,
    password
}: UseSipCallProps = {}) => {
    const [callState, setCallState] = useState<CallState>({ status: CallStatus.IDLE });
    const [isInitialized, setIsInitialized] = useState(false);
    const [isMuted, setIsMuted] = useState(false);

    // Các hàm xử lý cuộc gọi
    const initialize = useCallback(async (user?: string, pass?: string) => {
        // Khởi tạo SIP service
    }, [username, password]);

    const makeCall = useCallback(async (destination: string, options?: { skipMediaAccess?: boolean }) => {
        // Thực hiện cuộc gọi đi
    }, [isInitialized]);

    // Các hàm khác: answerCall, rejectCall, hangupCall, startRecording, stopRecording, toggleMute, disconnect

    // Đăng ký callback để nhận thông báo khi trạng thái cuộc gọi thay đổi
    useEffect(() => {
        // Xử lý thay đổi trạng thái và tự động kết nối
    }, [autoConnect, initialize, disconnect, isInitialized]);

    return {
        callState,
        isInitialized,
        isMuted,
        initialize,
        makeCall,
        answerCall,
        rejectCall,
        hangupCall,
        startRecording,
        stopRecording,
        toggleMute,
        disconnect
    };
};
```

### 4. sipService

Service này quản lý kết nối SIP và trạng thái cuộc gọi. Nó sử dụng thư viện SIP.js để giao tiếp với máy chủ SIP:

```typescript
class SipService {
    private simpleUser: Web.SimpleUser | null = null;
    private remoteAudio: HTMLAudioElement | null = null;
    private localStream: MediaStream | null = null;
    private callState: CallState = { status: CallStatus.IDLE };
    private onCallStateChangeCallbacks: ((state: CallState) => void)[] = [];

    constructor() {
        // Tạo audio element để phát âm thanh từ cuộc gọi
        this.remoteAudio = document.createElement('audio');
        this.remoteAudio.autoplay = true;
    }

    /**
     * Khởi tạo kết nối SIP
     */
    public async initialize(username: string = SIP_CONFIG.DEFAULT_USER, password: string = SIP_CONFIG.DEFAULT_PASSWORD): Promise<void> {
        // Khởi tạo kết nối SIP
    }

    // Các phương thức khác: makeCall, answerCall, rejectCall, hangupCall, startRecording, stopRecording, toggleMute, disconnect
}

// Export singleton instance
export const sipService = new SipService();
export default sipService;
```

## Các tính năng chính

### 1. Thực hiện cuộc gọi đi

Người dùng có thể thực hiện cuộc gọi đi bằng cách:
1. Nhập số điện thoại hoặc extension
2. Nhấn nút "Gọi" để thực hiện cuộc gọi với microphone
3. Hoặc nhấn "Chỉ nghe" để thực hiện cuộc gọi mà không cần microphone

### 2. Nhận cuộc gọi đến

Khi có cuộc gọi đến:
1. Giao diện hiển thị thông tin người gọi
2. Người dùng có thể chọn "Trả lời" hoặc "Từ chối"
3. Cũng có tùy chọn "Chỉ nghe" để trả lời mà không cần microphone

### 3. Điều khiển cuộc gọi

Trong khi cuộc gọi đang diễn ra, người dùng có thể:
1. Kết thúc cuộc gọi
2. Tắt/bật microphone
3. Bắt đầu/dừng ghi âm cuộc gọi

### 4. Chế độ "Chỉ nghe"

Một tính năng độc đáo là khả năng thực hiện hoặc trả lời cuộc gọi ở chế độ "chỉ nghe":
- Cho phép người dùng không có microphone tham gia cuộc gọi
- Hữu ích trong môi trường không thể nói chuyện
- Được triển khai bằng cách bỏ qua truy cập media trong quá trình thiết lập cuộc gọi

## Xử lý lỗi

Hệ thống xử lý các tình huống lỗi khác nhau:

- **Lỗi kết nối**: Vấn đề kết nối đến máy chủ SIP
- **Lỗi xác thực**: Thông tin đăng nhập không hợp lệ
- **Lỗi truy cập media**: Vấn đề truy cập microphone
- **Lỗi cuộc gọi**: Vấn đề trong quá trình thiết lập hoặc kết thúc cuộc gọi

Thông báo lỗi chi tiết được cung cấp để giúp người dùng khắc phục sự cố.

## Tích hợp WebRTC

Trung tâm cuộc gọi sử dụng WebRTC để xử lý media:

- **getUserMedia**: Yêu cầu quyền truy cập vào microphone của người dùng
- **RTCPeerConnection**: Thiết lập kết nối peer-to-peer
- **STUN Servers**: Giúp với việc vượt qua NAT để thiết lập kết nối

## Cách triển khai tính năng này trong dự án khác

### Bước 1: Cài đặt các thư viện cần thiết

```bash
npm install sip.js
# hoặc
yarn add sip.js
```

### Bước 2: Sao chép cấu trúc thư mục và các file

Sao chép toàn bộ thư mục `src/features/call-center` vào dự án mới của bạn.

### Bước 3: Cấu hình SIP

Cập nhật file `sipConfig.ts` với thông tin máy chủ SIP của bạn:

```typescript
export const SIP_CONFIG = {
    SERVER_URL: 'your-sip-server:port',
    WEBSOCKET_URL: 'ws://your-websocket-server:port/ws',
    // Các cấu hình khác
};
```

### Bước 4: Tích hợp vào ứng dụng

Trong file routes hoặc nơi bạn định nghĩa các trang:

```typescript
import { CallCenterPage } from 'features/call-center';

// Thêm route
<Route path="/call-center" element={<CallCenterPage />} />
```

### Bước 5: Tùy chỉnh giao diện người dùng

Tùy chỉnh `CallInterface.tsx` để phù hợp với thiết kế của ứng dụng của bạn.

## Các cân nhắc về bảo mật

- Thông tin đăng nhập SIP nên được lưu trữ an toàn
- Kết nối WebSocket nên sử dụng WSS (WebSockets bảo mật) trong môi trường sản xuất
- Quyền truy cập media được yêu cầu rõ ràng từ người dùng

## Khắc phục sự cố

Các vấn đề phổ biến và giải pháp:

1. **Không thể kết nối đến máy chủ SIP**
   - Kiểm tra kết nối mạng
   - Xác minh máy chủ SIP đang chạy
   - Đảm bảo tường lửa cho phép kết nối WebSocket

2. **Lỗi xác thực**
   - Kiểm tra tên người dùng và mật khẩu
   - Xác minh tài khoản SIP đang hoạt động

3. **Không có âm thanh**
   - Kiểm tra quyền truy cập microphone
   - Kiểm tra cài đặt âm thanh của trình duyệt
   - Đảm bảo không có ứng dụng khác đang sử dụng microphone

4. **Cuộc gọi bị ngắt kết nối**
   - Kiểm tra kết nối mạng
   - Xác minh máy chủ SIP vẫn hoạt động
   - Kiểm tra cấu hình ICE servers

## Kết luận

Tính năng Trung tâm cuộc gọi cung cấp một giải pháp dựa trên web để xử lý cuộc gọi thoại, loại bỏ nhu cầu về phần cứng điện thoại chuyên dụng. Bằng cách sử dụng WebRTC và SIP, nó cho phép nhân viên dịch vụ khách hàng thực hiện và nhận cuộc gọi trực tiếp từ trình duyệt web của họ.
