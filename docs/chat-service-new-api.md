# ChatService - New API: getMessagesByConversationId

## 📋 Tổng quan

Đã thêm API mới `getMessagesByConversationId` sử dụng endpoint `/api/messages/:conversationId` để lấy **TẤT CẢ** tin nhắn của một conversation. API này **KHÔNG PHÂN TRANG** và trả về toàn bộ dữ liệu.

## 🆕 API mới

### getMessagesByConversationId

```typescript
async getMessagesByConversationId(
  conversationId: string,
  params: MessageSearchParams = {}
): Promise<ChatMessage[]>
```

**Endpoint:** `/api/messages/:conversationId`

**Đặc điểm:**
- `conversationId` được truyền qua URL parameter (không qua query)
- **KHÔNG PHÂN TRANG** - trả về toàn bộ tin nhắn của conversation
- Hỗ trợ query filtering, sort, populate
- Return type: `ChatMessage[]` (array) thay vì `PaginatedResult`

## 🔄 So sánh với API cũ

### API cũ: getMessages (có phân trang)

```typescript
// Endpoint: /api/messages
// URL: /api/messages?page=1&pageSize=100&query={"conversationId":"abc123"}&populate=senderId
// Return: PaginatedResult<ChatMessage>

const result = await chatService.getMessages({
  page: 1,
  pageSize: 100,
  query: { conversationId: 'abc123' },
  populate: ['senderId']
});
// result.rows = ChatMessage[]
```

### API mới: getMessagesByConversationId (không phân trang)

```typescript
// Endpoint: /api/messages/:conversationId
// URL: /api/messages/abc123?populate=senderId
// Return: ChatMessage[]

const result = await chatService.getMessagesByConversationId('abc123', {
  populate: ['senderId']
  // Không cần page/pageSize
});
// result = ChatMessage[] (trực tiếp)
```

## 🚀 Cách sử dụng

### Ví dụ 1: Lấy tất cả tin nhắn cơ bản

```typescript
const messages = await chatService.getMessagesByConversationId('conv123', {
  populate: ['senderId']
});
// messages = ChatMessage[] (tất cả tin nhắn của conversation)
```

**URL được tạo:**
```
/api/messages/conv123?populate=senderId
```

### Ví dụ 2: Với query conditions

```typescript
const messages = await chatService.getMessagesByConversationId('conv123', {
  query: {
    read: false,
    senderRole: 'buyer'
  },
  sort: 'createdAt',
  populate: ['senderId']
});
// messages = ChatMessage[] (chỉ tin nhắn chưa đọc từ buyer)
```

**URL được tạo:**
```
/api/messages/conv123?query={"read":false,"senderRole":"buyer"}&sort=createdAt&populate=senderId
```

### Ví dụ 3: Với search

```typescript
const messages = await chatService.getMessagesByConversationId('conv123', {
  searchFields: ['text'],
  searchValue: 'help',
  sort: '-createdAt',
  populate: ['senderId']
});
// messages = ChatMessage[] (tin nhắn chứa từ "help", sắp xếp mới nhất trước)
```

**URL được tạo:**
```
/api/messages/conv123?searchFields=text&searchValue=help&sort=-createdAt&populate=senderId
```

## 📊 Cấu trúc Response

### API cũ (getMessages) - Có phân trang:
```typescript
interface PaginatedResult<ChatMessage> {
  rows: ChatMessage[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
```

### API mới (getMessagesByConversationId) - Không phân trang:
```typescript
// Trả về trực tiếp array
ChatMessage[]
```

## 🔧 Implementation Details

### Endpoint Constants

```typescript
const API = {
  // ... existing endpoints
  MESSAGES_BY_CONVERSATION_ID: '/messages/:conversationId',
  // ... other endpoints
};
```

### Method Implementation

```typescript
async getMessagesByConversationId(
  conversationId: string,
  params: MessageSearchParams = {}
): Promise<PaginatedResult<ChatMessage>> {
  // Build query parameters (không cần conversationId trong query vì đã có trong URL)
  const queryObject: any = {
    ...params.query // Additional query conditions
  };

  const query: QueryParams = {
    page: params.page || 1,
    pageSize: params.pageSize || 20,
    query: Object.keys(queryObject).length > 0 ? queryObject : undefined,
    sort: params.sort || 'createdAt',
    searchFields: params.searchFields,
    searchValue: params.searchValue
  };

  const result = await this.getAllPaginate<ChatMessage>(
    API.MESSAGES_BY_CONVERSATION_ID.replace(':conversationId', conversationId),
    query,
    params.populate || ['senderId'],
    true,
    false
  );

  // Convert and return...
}
```

## 🎯 Lợi ích

1. **Cleaner URLs**: ConversationId trong URL thay vì query
2. **RESTful Design**: Tuân theo REST conventions
3. **Better Performance**: Backend có thể optimize dễ hơn
4. **Flexible Queries**: Vẫn hỗ trợ đầy đủ query features
5. **Backward Compatible**: Không ảnh hưởng đến API cũ

## 📝 Usage in ChatPage

### Trước:

```typescript
const result = await chatService.getMessages(conversationId, {
  page: 1,
  pageSize: 100,
  populate: ['senderId']
});
```

### Sau:

```typescript
const result = await chatService.getMessagesByConversationId(conversationId, {
  page: 1,
  pageSize: 100,
  populate: ['senderId']
});
```

## ⚠️ Lưu ý

1. **ConversationId** không cần truyền trong `params.query` vì đã có trong URL
2. **Additional query conditions** vẫn có thể truyền qua `params.query`
3. **Sort, search, populate** hoạt động bình thường
4. **Response format** giống hệt API cũ

## 🔄 Migration

ChatPage đã được cập nhật để sử dụng API mới:

```typescript
// In handleSelectConversation
const result = await chatService.getMessagesByConversationId(conversationId, {
  page: 1,
  pageSize: 100,
  populate: ['senderId']
});
```

API này sẽ tạo ra URL sạch hơn và tuân theo RESTful conventions!
