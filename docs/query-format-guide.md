# Query Format Guide - BaseService getAllPaginate

## 📋 Tổng quan

BaseService đã được cập nhật để hỗ trợ query format phức tạp như bạn yêu cầu:

```
?page=1&pageSize=10&query={%22gptModel%22:%22gpt-4o-mini%22,%22outputTypeId%22:%22669f538f1420505310174aa5%22}&sort=-createdAt&populate=optionIds,outputTypeId&searchFields=shortName
```

## 🔧 Cách hoạt động

### 1. **Query Object**
- Tham số `query` sẽ được JSON stringify và URL encode
- Chứa các điều kiện filter phức tạp

### 2. **Sort Parameter**
- Hỗ trợ sort theo nhiều trường
- Prefix `-` để sort descending

### 3. **Populate Parameter**
- Populate các trường liên quan
- <PERSON><PERSON> thể truyền array hoặc string

### 4. **Search Fields**
- Chỉ định các trường để search
- Có thể truyền array hoặc string

## 🚀 Cách sử dụng

### Ví dụ 1: Query cơ bản

```typescript
const result = await someService.getAllPaginate('/api/items', {
  page: 1,
  pageSize: 10,
  query: {
    gptModel: 'gpt-4o-mini',
    outputTypeId: '669f538f1420505310174aa5'
  },
  sort: '-createdAt',
  searchFields: ['shortName'],
  searchValue: 'test'
}, ['optionIds', 'outputTypeId']);
```

**Kết quả URL:**
```
/api/items?page=1&pageSize=10&query=%7B%22gptModel%22%3A%22gpt-4o-mini%22%2C%22outputTypeId%22%3A%22669f538f1420505310174aa5%22%7D&sort=-createdAt&searchFields=shortName&searchValue=test&populate=optionIds,outputTypeId
```

### Ví dụ 2: Query với nhiều điều kiện

```typescript
const result = await someService.getAllPaginate('/api/products', {
  page: 1,
  pageSize: 20,
  query: {
    category: 'electronics',
    price: { $gte: 100, $lte: 500 },
    inStock: true,
    tags: { $in: ['featured', 'sale'] }
  },
  sort: '-createdAt,name',
  searchFields: ['name', 'description'],
  searchValue: 'smartphone'
}, ['category', 'reviews']);
```

### Ví dụ 3: Sử dụng trong ChatService

```typescript
// Trong ChatService
async getMessages(
  conversationId: string,
  params: MessageSearchParams = {}
): Promise<PaginatedResult<ChatMessage>> {
  const query: QueryParams = {
    page: params.page || 1,
    pageSize: params.pageSize || 20,
    query: {
      conversationId,
      ...params.query // Có thể thêm các filter khác
    },
    sort: params.sort || '-createdAt'
  };

  const result = await this.getAllPaginate<ChatMessage>(
    API.MESSAGES,
    query,
    params.populate || ['senderId'],
    true,
    false
  );

  // Convert và return...
}
```

## 📝 Các tham số được hỗ trợ

### QueryParams Interface

```typescript
export interface QueryParams {
  [key: string]: any;
  query?: object;           // Complex query object (JSON stringified)
  sort?: string;           // Sort parameter like "-createdAt" or "name,createdAt"
  limit?: number;          // Limit results
  searchFields?: string | string[]; // Search fields
  searchValue?: string;    // Search value
}
```

### Ví dụ các giá trị:

```typescript
// Sort examples
sort: '-createdAt'           // Descending by createdAt
sort: 'name'                 // Ascending by name
sort: '-createdAt,name'      // Multiple fields

// Query examples
query: { status: 'active' }
query: { 
  price: { $gte: 100, $lte: 500 },
  category: { $in: ['electronics', 'books'] }
}

// SearchFields examples
searchFields: 'name'
searchFields: ['name', 'description', 'tags']
```

## 🔄 Migration từ format cũ

### Trước:
```typescript
const params = {
  page: 1,
  pageSize: 10,
  conversationId: 'abc123',
  status: 'active'
};
```

### Sau:
```typescript
const params = {
  page: 1,
  pageSize: 10,
  query: {
    conversationId: 'abc123',
    status: 'active'
  }
};
```

## ⚠️ Lưu ý

1. **Query object** sẽ được JSON stringify, nên chỉ chứa các giá trị serializable
2. **Sort parameter** có thể chứa nhiều trường, phân cách bằng dấu phẩy
3. **SearchFields** có thể là string hoặc array
4. **Populate** vẫn được truyền riêng như parameter thứ 3

## 🎯 Kết quả

Với cách này, bạn có thể tạo ra các query URL phức tạp như:

```
?page=1&pageSize=10&query={%22gptModel%22:%22gpt-4o-mini%22,%22outputTypeId%22:%22669f538f1420505310174aa5%22}&sort=-createdAt&populate=optionIds,outputTypeId&searchFields=shortName&searchValue=test
```

Hoàn toàn phù hợp với yêu cầu của bạn!
