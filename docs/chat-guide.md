Dưới đây là **tài liệu chi tiết cho dev frontend** để thực hiện trang `ChatPage` dành cho nhân viên **chăm sóc khách hàng (admin)** trong hệ thống chat React + Node.js + Moleculer + Socket.IO.

---

# 📄 Tài liệu phát triển trang `ChatPage` (vai trò: Admin)

## ✅ 1. <PERSON><PERSON><PERSON> tiêu trang ChatPage

Trang `ChatPage` cho phép nhân viên chăm sóc khách hàng:

* Xem danh sách các cuộc trò chuyện với người mua/shop
* Xem nội dung chi tiết từng cuộc trò chuyện
* Gửi/nhận tin nhắn theo thời gian thực

---

## 🏗️ 2. <PERSON><PERSON><PERSON> trúc giao diện

Gồ<PERSON> 3 phần chính:

```
+---------------------------------------------------+
| Sidebar: Danh sách cuộc trò chuyện                |
+---------------------------------------------------+
| ChatHeader: Thông tin người chat                  |
| ChatContent: Danh sách tin nhắn                   |
| ChatInput: Nhập và gửi tin nhắn                   |
+---------------------------------------------------+
```

---

## 📁 3. Cấu trúc component

```bash
src/
├── pages/
│   ├── chats
│   │   └── ChatPage.jsx       # Trang chat chính
│   ├── components/
│   │   ├── ChatSidebar.jsx  # Danh sách cuộc trò chuyện
│   │   ├── ChatHeader.jsx   # Thông tin người chat
│   │   ├── ChatContent.jsx  # Danh sách tin nhắn
│   │   └── ChatInput.jsx    # Nhập và gửi tin nhắn

// thêm vào service
├── services/
│   ├── ChatService.js       # Service chat  # REST API
│   └── socket.js            # Socket.IO client
```

---

## 🔌 4. Kết nối socket

### `services/socket.js`:

```js
import { io } from "socket.io-client";
const socket = io("http://localhost:3001"); // địa chỉ service socket
export default socket;
```

---

## 📦 5. API cần dùng

### 5.1 Lấy danh sách cuộc trò chuyện

```ts
GET /api/conversations/listByUser?userId=xxx
```

Trả về:

```ts
[
  {
    _id: "conversationId",
    participants: [ { _id, name, role } ],
    lastMessage: { content, createdAt },
    updatedAt: "timestamp"
  },
  ...
]
```

### 5.2 Lấy danh sách tin nhắn

```ts
GET /api/messages/list?conversationId=xxx
```

### 5.3 Gửi tin nhắn (qua socket)

```ts
socket.emit("sendMessage", {
  conversationId,
  senderId,
  content,
});
```

---

## 🔁 6. Lifecycle hoạt động

| Giai đoạn             | Hành động                                                    |
| --------------------- | ------------------------------------------------------------ |
| Khi load ChatPage     | Gọi API lấy danh sách cuộc trò chuyện                        |
| Khi click 1 hội thoại | Gọi API lấy tin nhắn + `socket.emit("join", conversationId)` |
| Khi gửi tin           | `socket.emit("sendMessage", ...)`                            |
| Khi nhận tin          | `socket.on("newMessage", msg => ...)`                        |

---

## 🧠 7. Trạng thái React (có thể dùng Zustand)

### State cần quản lý:

```ts
{
  user: { _id, name },
  activeConversationId: string,
  conversations: [],
  messages: []
}
```

---

## 💬 8. ChatPage: luồng thực hiện

### `ChatPage.jsx`

```jsx
useEffect(() => {
  fetchConversations();

  socket.on("newMessage", (msg) => {
    if (msg.conversationId === activeConversationId) {
      setMessages(prev => [...prev, msg]);
    }
  });

  return () => socket.off("newMessage");
}, [activeConversationId]);

const handleSelectConversation = async (conversationId) => {
  setActiveConversationId(conversationId);
  socket.emit("join", conversationId);
  const res = await axios.get("/api/messages/list", { params: { conversationId } });
  setMessages(res.data);
};
```

---

## 🧾 9. Yêu cầu UI/UX

* Tin nhắn bên trái/phải tùy người gửi
* Tự động scroll xuống dưới khi có tin mới
* Hiển thị thời gian, avatar, phân biệt vai trò
* Loading indicator khi chưa chọn hội thoại

---

## 🚀 10. Gợi ý mở rộng tương lai

* Gửi ảnh/file
* Gửi emoji
* Trạng thái online/offline
* Support typing indicator ("đang nhập...")

---

## 📌 Ghi chú cho Dev Frontend

* Phải có `userId` từ login hoặc route (mock cũng được)
* Khi chuyển hội thoại khác, phải:

    1. Gọi API lấy messages
    2. Emit socket `"join"`
* Socket phải duy trì suốt phiên (singleton)
* Hiện tại chỉ support text – gửi ảnh để sau

---

Bạn có thể gửi tài liệu này qua Notion, PDF hoặc trực tiếp qua repo README cho dev frontend.

---

Bạn có muốn mình **tạo luôn bản React mẫu** cho `ChatPage.jsx` với các component đã gọi được API và socket chưa?
