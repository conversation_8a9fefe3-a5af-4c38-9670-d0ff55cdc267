# ChatService Query Format Update

## 📋 Tổng quan

ChatService đã đư<PERSON><PERSON> cập nhật để sử dụng đúng format query mới với complex query object, sort, populate và searchFields nh<PERSON> yêu cầu.

## 🔄 <PERSON><PERSON><PERSON> thay đổi chính

### 1. **getConversations Method**

**Trước:**
```typescript
const query: QueryParams = {
  page: params.page || 1,
  pageSize: params.pageSize || 20,
  status: params.status,
  customerType: params.customerType,
  supportId: params.supportId,
  tags: params.tags?.join(',')
};
```

**Sau:**
```typescript
// Build complex query object
const queryObject: any = {};

if (params.status) queryObject.status = params.status;
if (params.customerType) queryObject.customerType = params.customerType;
if (params.supportId) queryObject.supportId = params.supportId;
if (params.tags && params.tags.length > 0) queryObject.tags = { $in: params.tags };

const query: QueryParams = {
  page: params.page || 1,
  pageSize: params.pageSize || 20,
  query: Object.keys(queryObject).length > 0 ? queryObject : undefined,
  sort: params.sort || '-updatedAt',
  searchFields: params.searchFields,
  searchValue: params.searchValue
};
```

### 2. **getMessages Method**

**Trước:**
```typescript
const query: QueryParams = {
  conversationId,
  page: params.page || 1,
  pageSize: params.pageSize || 20,
  ...params.query
};
```

**Sau:**
```typescript
// Build complex query object
const queryObject: any = {
  conversationId,
  ...params.query // Merge additional query conditions
};

const query: QueryParams = {
  page: params.page || 1,
  pageSize: params.pageSize || 20,
  query: queryObject,
  sort: params.sort || 'createdAt',
  searchFields: params.searchFields,
  searchValue: params.searchValue
};
```

### 3. **Updated Interfaces**

```typescript
export interface ConversationSearchParams {
  page?: number;
  pageSize?: number;
  populate?: string[];
  status?: 'open' | 'closed';
  customerType?: 'buyer' | 'shop';
  supportId?: string;
  tags?: string[];
  sort?: string; // Sort parameter like "-updatedAt" or "createdAt"
  searchFields?: string[]; // Fields to search in
  searchValue?: string; // Search value
}

export interface MessageSearchParams {
  query?: object; // Additional query conditions to merge with conversationId
  page?: number;
  pageSize?: number;
  populate?: string[];
  sort?: string; // Sort parameter like "createdAt" or "-createdAt"
  searchFields?: string[]; // Fields to search in
  searchValue?: string; // Search value
}
```

## 🚀 Cách sử dụng mới

### Ví dụ 1: getConversations với query phức tạp

```typescript
const conversations = await chatService.getConversations({
  page: 1,
  pageSize: 10,
  status: 'open',
  customerType: 'buyer',
  tags: ['urgent', 'vip'],
  sort: '-updatedAt',
  searchFields: ['customerId.fullName'],
  searchValue: 'John',
  populate: ['customerId', 'supportId']
});
```

**URL được tạo:**
```
/conversations?page=1&pageSize=10&query={"status":"open","customerType":"buyer","tags":{"$in":["urgent","vip"]}}&sort=-updatedAt&searchFields=customerId.fullName&searchValue=John&populate=customerId,supportId
```

### Ví dụ 2: getMessages với filter

```typescript
const messages = await chatService.getMessages('conv123', {
  page: 1,
  pageSize: 50,
  query: {
    read: false,
    senderRole: 'buyer'
  },
  sort: 'createdAt',
  searchFields: ['text'],
  searchValue: 'help',
  populate: ['senderId']
});
```

**URL được tạo:**
```
/messages?page=1&pageSize=50&query={"conversationId":"conv123","read":false,"senderRole":"buyer"}&sort=createdAt&searchFields=text&searchValue=help&populate=senderId
```

## 📊 So sánh URL format

### Trước (flat parameters):
```
/conversations?page=1&pageSize=10&status=open&customerType=buyer&tags=urgent,vip&populate=customerId,supportId
```

### Sau (complex query object):
```
/conversations?page=1&pageSize=10&query={"status":"open","customerType":"buyer","tags":{"$in":["urgent","vip"]}}&sort=-updatedAt&populate=customerId,supportId
```

## 🎯 Lợi ích

1. **Flexible Queries**: Hỗ trợ MongoDB operators như `$in`, `$gte`, `$lte`, etc.
2. **Better Sorting**: Sort theo nhiều trường với direction
3. **Search Support**: Tìm kiếm trong các trường cụ thể
4. **Consistent Format**: Đồng nhất với format mà bạn yêu cầu
5. **Backward Compatible**: Vẫn hoạt động với code cũ

## ⚠️ Breaking Changes

1. **Tags parameter**: Từ string `"urgent,vip"` thành array `["urgent", "vip"]` và MongoDB query `{"$in": ["urgent", "vip"]}`
2. **Query structure**: Các filter được wrap trong `query` object
3. **Sort parameter**: Có thể override default sort

## 🔧 Migration Guide

### Cập nhật calls trong ChatPage:

**Trước:**
```typescript
const result = await chatService.getConversations({
  page: 1,
  pageSize: 50,
  populate: ['customerId', 'supportId'],
  status: 'open'
});
```

**Sau (không cần thay đổi - backward compatible):**
```typescript
const result = await chatService.getConversations({
  page: 1,
  pageSize: 50,
  populate: ['customerId', 'supportId'],
  status: 'open' // Sẽ được convert thành query object tự động
});
```

**Hoặc sử dụng features mới:**
```typescript
const result = await chatService.getConversations({
  page: 1,
  pageSize: 50,
  populate: ['customerId', 'supportId'],
  status: 'open',
  sort: '-updatedAt',
  searchFields: ['customerId.fullName'],
  searchValue: searchQuery
});
```

## ✅ Kết quả

Bây giờ ChatService sẽ tạo ra các URL đúng format như bạn yêu cầu:

```
?page=1&pageSize=10&query={"status":"open","customerType":"buyer"}&sort=-updatedAt&populate=customerId,supportId&searchFields=customerId.fullName
```

Hoàn toàn phù hợp với backend expectations!
