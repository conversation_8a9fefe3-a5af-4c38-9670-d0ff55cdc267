# BaseService - H<PERSON>ớng dẫn sử dụng (Updated)

## 📋 Tổng quan

BaseService đã được viết lại hoàn toàn theo specification từ `docs/Base_Service_Documentation.md`. Nó cung cấp các method CRUD cơ bản với đầy đủ tính năng như loading state, error handling, data conversion và populate options.

## 🏗️ Cấu trúc mới

### Interfaces chính

```typescript
// Response API cơ bản
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

// Tham số phân trang
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  limit?: number;
}

// Query parameters
export interface QueryParams {
  [key: string]: any;
  sort?: string;
  limit?: number;
}

// Request config
export interface RequestConfig {
  loading?: boolean;
  toastError?: boolean;
  hideNoti?: boolean;
  params?: any;
}

// Kết quả phân trang
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
```

### Utility Functions

```typescript
// Generate populate parameter
genPopulateParam(populateOpts: string[] = []): string

// Generate query parameter
genQueryParam(query: QueryParams = {}): string

// Generate search field parameter
genSearchFieldParam(searchFields: string[] = [], searchValue?: string): string

// Convert snake_case to camelCase
convertSnakeCaseToCamelCase(obj: any): any
```

## 🚀 Các Method chính

### CRUD Operations

#### 1. Create
```typescript
async create<R = T>(
  apiEndpoint: string,
  data: Partial<T>,
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false,
  params?: any
): Promise<R | null>
```

#### 2. Get All
```typescript
async getAll<R = T>(
  apiEndpoint: string,
  query: QueryParams = {},
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<R[] | null>
```

#### 3. Get All Paginate
```typescript
async getAllPaginate<R = T>(
  apiEndpoint: string,
  query: QueryParams & PaginationParams = {},
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<PaginatedResponse<R> | null>
```

#### 4. Get Detail
```typescript
async getDetail<R = T>(
  apiEndpoint: string,
  id: string,
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<R | null>
```

#### 5. Update
```typescript
async update<R = T>(
  apiEndpoint: string,
  data: Partial<T>,
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<R | null>
```

#### 6. Delete
```typescript
async delete(
  apiEndpoint: string,
  id: string,
  loading: boolean = true,
  toastError: boolean = false
): Promise<any>
```

### Search Operations

#### 7. Search
```typescript
async search<R = T>(
  apiEndpoint: string,
  searchFields: string[] = [],
  searchValue?: string,
  query: QueryParams = {},
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<R[] | null>
```

#### 8. Search Paginate
```typescript
async searchPaginate<R = T>(
  apiEndpoint: string,
  searchFields: string[] = [],
  searchValue?: string,
  query: QueryParams & PaginationParams = {},
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<PaginatedResponse<R> | null>
```

### Bulk Operations

#### 9. Bulk Create
```typescript
async bulkCreate<R = T>(
  apiEndpoint: string,
  dataArray: Partial<T>[],
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<R[] | null>
```

#### 10. Bulk Update
```typescript
async bulkUpdate<R = T>(
  apiEndpoint: string,
  dataArray: Partial<T>[],
  populateOpts: string[] = [],
  loading: boolean = true,
  toastError: boolean = false
): Promise<R[] | null>
```

#### 11. Bulk Delete
```typescript
async bulkDelete(
  apiEndpoint: string,
  ids: string[],
  loading: boolean = true,
  toastError: boolean = false
): Promise<any>
```

### Utility Methods

#### 12. Count
```typescript
async count<R = number>(
  apiEndpoint: string,
  query: QueryParams = {},
  loading: boolean = true,
  toastError: boolean = false
): Promise<R | null>
```

#### 13. Exists
```typescript
async exists(
  apiEndpoint: string,
  id: string,
  loading: boolean = false,
  toastError: boolean = false
): Promise<boolean>
```

#### 14. Upload File
```typescript
async uploadFile<R = any>(
  apiEndpoint: string,
  file: File,
  additionalData?: Record<string, any>,
  loading: boolean = true,
  toastError: boolean = false
): Promise<R | null>
```

#### 15. Download File
```typescript
async downloadFile(
  apiEndpoint: string,
  filename?: string,
  loading: boolean = true,
  toastError: boolean = false
): Promise<Blob | null>
```

## 🎯 Ví dụ sử dụng

### ProjectService Implementation

```typescript
export class ProjectService extends BaseService<Project> {
  constructor() {
    super('/api/projects');
  }

  async createProject(data: CreateProjectData): Promise<Project | null> {
    return this.create<Project>('/api/projects', data, ['ownerId', 'imageId'], true, true);
  }

  async getAllProjects(query: QueryParams = {}): Promise<Project[] | null> {
    return this.getAll<Project>('/api/projects', query, ['ownerId', 'imageId']);
  }

  async getProjectDetail(id: string): Promise<Project | null> {
    return this.getDetail<Project>('/api/projects/:id', id, ['ownerId', 'imageId']);
  }

  async updateProject(data: Project): Promise<Project | null> {
    return this.update<Project>('/api/projects/:id', data, [], true, true);
  }

  async deleteProject(id: string): Promise<any> {
    return this.delete('/api/projects/:id', id, true, true);
  }

  async searchProjects(searchValue: string): Promise<Project[] | null> {
    return this.search<Project>(
      '/api/projects/search',
      ['name', 'description'],
      searchValue,
      {},
      ['ownerId', 'imageId']
    );
  }
}
```

### Sử dụng trong Component

```typescript
import { projectService } from '@/services/ProjectService';

const ProjectList: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);

  const loadProjects = async () => {
    const result = await projectService.getAllProjects({
      sort: 'createdAt',
      limit: 10
    });
    
    if (result) {
      setProjects(result);
    }
  };

  const handleCreate = async (data: CreateProjectData) => {
    const result = await projectService.createProject(data);
    if (result) {
      loadProjects(); // Reload list
    }
  };

  const handleDelete = async (id: string) => {
    const result = await projectService.deleteProject(id);
    if (result) {
      loadProjects(); // Reload list
    }
  };

  // Component render...
};
```

## 🔧 Tính năng mới

1. **Automatic Data Conversion**: Tự động convert snake_case sang camelCase
2. **Loading State Management**: Tự động quản lý loading state
3. **Error Handling**: Tùy chọn hiển thị toast error
4. **Populate Support**: Hỗ trợ populate related data
5. **Search Functionality**: Tìm kiếm với multiple fields
6. **Bulk Operations**: Thao tác hàng loạt
7. **File Upload/Download**: Hỗ trợ upload và download file
8. **Type Safety**: Đầy đủ TypeScript support

## 💡 Best Practices

1. **Luôn định nghĩa API constants** để tránh hardcode
2. **Sử dụng populate** để load related data
3. **Enable loading và toastError** cho user experience tốt hơn
4. **Sử dụng generic types** để type-safe
5. **Handle null responses** properly trong component
6. **Sử dụng search methods** thay vì filter client-side
7. **Implement proper error boundaries** trong React

## 🚨 Breaking Changes

- Tất cả method names đã thay đổi (không còn suffix `Base`)
- Tham số method đã thay đổi hoàn toàn
- Response format đã thay đổi
- Cần update tất cả existing services để sử dụng API mới
