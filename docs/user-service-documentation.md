# Tài liệu User Service

## Giới thiệu

Tài liệu này mô tả các API endpoints và chức năng của User Service trong hệ thống. User Service cung cấp các chức năng quản lý người dùng như đăng ký, đ<PERSON><PERSON> nhậ<PERSON>, quản lý thông tin cá nhân, và các chức năng liên quan đến xác thực.

## Mô hình dữ liệu (User Model)

User model bao gồm các trường thông tin sau:

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| _id | ObjectId | ID của người dùng |
| fullName | String | Họ và tên đầy đủ |
| email | String | Địa chỉ email (duy nhất, bắt buộc) |
| password | String | Mật khẩu đã được mã hóa |
| gender | String | Giới tính |
| phone | String | Số điện thoại |
| avatar | String | URL ảnh đại diện |
| imageAvatarId | ObjectId | ID của ảnh đại diện (tham chiếu đến bảng IMAGE) |
| organizationId | ObjectId | ID của tổ chức (tham chiếu đến bảng ORGANIZATION) |
| isDeleted | Boolean | Trạng thái đã xóa hay chưa (mặc định: false) |
| roleId | Array[ObjectId] | Danh sách ID vai trò (tham chiếu đến bảng ROLE) |
| isSystemAdmin | Boolean | Có phải admin hệ thống không (mặc định: false) |
| active | Boolean | Trạng thái kích hoạt (mặc định: false) |
| neverLogin | Boolean | Chưa từng đăng nhập (mặc định: true) |
| lastLogin | Date | Thời gian đăng nhập gần nhất |
| lastVisit | Date | Thời gian truy cập gần nhất |
| lastChangePassword | Date | Thời gian thay đổi mật khẩu gần nhất |
| deviceTokens | Array | Danh sách token thiết bị |
| role | String | Vai trò ("admin", "normal", "contributor", mặc định: "normal") |
| type | String | Loại người dùng ("student", "teacher", mặc định: "student") |
| persona | Array[String] | Danh sách persona của người dùng |
| isDeveloper | Boolean | Có phải developer không (mặc định: false) |
| state | String | Trạng thái ("active", "waitlist", mặc định: "active") |
| hasPassword | Boolean | Có mật khẩu hay không (mặc định: true) |
| createdAt | Date | Thời gian tạo |
| updatedAt | Date | Thời gian cập nhật gần nhất |

## API Endpoints

### Đăng nhập

- **Endpoint:** `POST /api/users/login`
- **Mô tả:** Đăng nhập với email và mật khẩu
- **Tham số:**
  - `email` (String): Địa chỉ email
  - `password` (String): Mật khẩu
  - `deviceToken` (String, tùy chọn): Token thiết bị
- **Phản hồi:**
  ```json
  {
    "user": {
      "_id": "...",
      "fullName": "...",
      "email": "...",
      "role": "...",
      "avatar": "...",
      "lastLogin": "..."
      // Các thông tin khác của người dùng (không bao gồm mật khẩu)
    }
  }
  ```
- **Lưu ý:** API này sẽ thiết lập cookie chứa access token và refresh token

### Đăng ký

- **Endpoint:** `POST /api/users/register`
- **Mô tả:** Đăng ký tài khoản mới
- **Tham số:**
  - `email` (String): Địa chỉ email
  - `fullName` (String): Họ và tên đầy đủ
  - `password` (String): Mật khẩu (tối thiểu 6 ký tự)
  - `phone` (String, tùy chọn): Số điện thoại
  - `gender` (String, tùy chọn): Giới tính ("male", "female", "other")
- **Phản hồi:**
  ```json
  {
    "user": {
      "_id": "...",
      "fullName": "...",
      "email": "...",
      "role": "user",
      // Các thông tin khác của người dùng
    }
  }
  ```

### Lấy thông tin người dùng hiện tại

- **Endpoint:** `GET /api/users/me`
- **Mô tả:** Lấy thông tin của người dùng đã đăng nhập
- **Yêu cầu xác thực:** Có
- **Phản hồi:**
  ```json
  {
    "_id": "...",
    "fullName": "...",
    "email": "...",
    "role": "...",
    "avatar": "...",
    "hasPassword": true,
    "version": "1.3.1 - 2025-04-01__01"
    // Các thông tin khác của người dùng (không bao gồm mật khẩu)
  }
  ```

### Cập nhật thông tin cá nhân

- **Endpoint:** `PATCH /api/users/info`
- **Mô tả:** Cập nhật thông tin cá nhân của người dùng đã đăng nhập
- **Yêu cầu xác thực:** Có
- **Tham số:**
  - `fullName` (String, tùy chọn): Họ và tên đầy đủ
  - `email` (String, tùy chọn): Địa chỉ email
  - `phone` (String, tùy chọn): Số điện thoại
  - `gender` (String, tùy chọn): Giới tính
  - `avatar` (String, tùy chọn): URL ảnh đại diện
- **Lưu ý:** Không thể cập nhật các trường nhạy cảm như password, role, permissions, v.v.
- **Phản hồi:** Thông tin người dùng đã được cập nhật

### Quên mật khẩu

- **Endpoint:** `POST /api/users/forgotPassword`
- **Mô tả:** Gửi email đặt lại mật khẩu
- **Tham số:**
  - `email` (String): Địa chỉ email
- **Phản hồi:**
  ```json
  {
    "success": true,
    "message": "Email đặt lại mật khẩu đã được gửi"
  }
  ```

### Đặt lại mật khẩu

- **Endpoint:** `POST /api/users/resetPassword`
- **Mô tả:** Đặt lại mật khẩu bằng token
- **Tham số:**
  - `token` (String): Token đặt lại mật khẩu
  - `password` (String): Mật khẩu mới
- **Phản hồi:**
  ```json
  {
    "user": {
      // Thông tin người dùng
    }
  }
  ```

### Tìm người dùng theo email

- **Endpoint:** `GET /api/users/findOne`
- **Mô tả:** Tìm người dùng theo địa chỉ email
- **Yêu cầu xác thực:** Có
- **Tham số:**
  - `email` (String): Địa chỉ email
- **Phản hồi:** Thông tin người dùng (không bao gồm mật khẩu)

### Lấy danh sách người dùng theo tổ chức

- **Endpoint:** `GET /api/users/getListUserByOrganization`
- **Mô tả:** Lấy danh sách người dùng thuộc tổ chức của người dùng hiện tại
- **Yêu cầu xác thực:** Có
- **Yêu cầu vai trò:** ORG_ADMIN
- **Phản hồi:** Danh sách người dùng thuộc tổ chức

## Xác thực và Bảo mật

### Access Token và Refresh Token

- Hệ thống sử dụng JWT (JSON Web Token) để xác thực người dùng
- Access token có thời hạn 72 giờ
- Refresh token được lưu trong cookie và có thể được sử dụng để lấy access token mới
- Cả access token và refresh token đều được lưu trong cookie của trình duyệt

### Yêu cầu xác thực

- Các API yêu cầu xác thực cần gửi kèm access token trong cookie hoặc header Authorization
- Format header: `Authorization: Bearer {access_token}`

## Lưu ý cho Frontend

1. **Xử lý cookie:** Hệ thống sử dụng cookie để lưu trữ token, đảm bảo rằng frontend được cấu hình để gửi cookie trong các request.

2. **Xử lý lỗi:** Kiểm tra mã lỗi và thông báo lỗi trong phản hồi để hiển thị thông báo phù hợp cho người dùng.

3. **Tải ảnh đại diện:** Ảnh đại diện có thể được tải lên thông qua API riêng và lưu URL hoặc ID trong thông tin người dùng.

4. **Kiểm tra vai trò:** Một số API yêu cầu vai trò cụ thể, đảm bảo kiểm tra vai trò người dùng trước khi gọi API.

5. **Đăng xuất:** Khi đăng xuất, cần xóa cookie và token lưu trữ ở phía client.
