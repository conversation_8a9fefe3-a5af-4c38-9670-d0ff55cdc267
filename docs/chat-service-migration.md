# ChatService Migration - Sử dụng BaseService getAllPaginate

## 📋 Tổng quan

ChatService đã được cập nhật để kế thừa từ BaseService và sử dụng method `getAllPaginate` thay vì implementation cũ. <PERSON>iều này mang lại nhiều lợi ích như loading state management, error handling, và data conversion tự động.

## 🔄 Các thay đổi chính

### 1. ChatService Class Structure

**Trước:**
```typescript
class ChatService {
  async getMessages(params: MessageSearchParams = {}): Promise<PaginatedResult<ChatMessage>> {
    const queryParams = new URLSearchParams();
    // Manual query building...
    return api.get<PaginatedResult<ChatMessage>>(`/messages?${queryParams.toString()}`);
  }
}
```

**Sau:**
```typescript
class ChatService extends BaseService<ChatMessage> {
  constructor() {
    super('/messages');
  }

  async getMessages(
    conversationId: string,
    params: MessageSearchParams = {}
  ): Promise<PaginatedResponse<ChatMessage> | null> {
    const query: QueryParams = {
      conversationId,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      ...params.query
    };

    return this.getAllPaginate<ChatMessage>(
      API.MESSAGES,
      query,
      params.populate || ['senderId'],
      true,
      false
    );
  }
}
```

### 2. Method Signature Changes

#### getMessages Method

**Trước:**
```typescript
async getMessages(params: MessageSearchParams = {}): Promise<PaginatedResult<ChatMessage>>
```

**Sau:**
```typescript
async getMessages(
  conversationId: string,
  params: MessageSearchParams = {}
): Promise<PaginatedResponse<ChatMessage> | null>
```

**Thay đổi:**
- Thêm tham số `conversationId` bắt buộc
- Return type từ `PaginatedResult` → `PaginatedResponse`
- Có thể return `null` nếu có lỗi

#### sendMessage Method

**Trước:**
```typescript
async sendMessage(conversationId: string, text: string, attachments: MessageAttachment[] = []): Promise<ChatMessage>
```

**Sau:**
```typescript
async sendMessage(
  conversationId: string,
  text: string,
  attachments: MessageAttachment[] = []
): Promise<ChatMessage | null>
```

**Thay đổi:**
- Có thể return `null` nếu có lỗi
- Sử dụng BaseService.create() với loading và error handling

### 3. Response Format Changes

#### PaginatedResult vs PaginatedResponse

**PaginatedResult (Backend format):**
```typescript
interface PaginatedResult<T> {
  rows: T[];  // Backend trả về 'rows' thay vì 'data'
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
```

**PaginatedResponse (BaseService format):**
```typescript
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
```

### 4. API Constants

Thêm API constants để tránh hardcode:

```typescript
const API = {
  CONVERSATIONS: '/conversations',
  CONVERSATION_ID: '/conversations/:id',
  MESSAGES: '/messages',
  MESSAGE_ID: '/messages/:id',
  MESSAGES_BY_CONVERSATION: '/messages/conversation/:conversationId',
  FILES_UPLOAD: '/files/upload'
};
```

## 🚀 Tính năng mới

### 1. Automatic Loading State
```typescript
// Loading state được quản lý tự động
const result = await chatService.getMessages(conversationId, {
  page: 1,
  pageSize: 20
}); // Loading indicator sẽ hiển thị tự động
```

### 2. Error Handling
```typescript
// Error handling tự động với toast notification
const result = await chatService.sendMessage(conversationId, text, attachments);
if (!result) {
  // Xử lý khi có lỗi
  console.log('Failed to send message');
}
```

### 3. Data Conversion
```typescript
// Tự động convert snake_case sang camelCase
const messages = await chatService.getMessages(conversationId);
// Dữ liệu đã được convert format tự động
```

### 4. Search Functionality
```typescript
// Tìm kiếm tin nhắn mới
const searchResults = await chatService.searchMessages(
  'hello world',
  conversationId,
  { page: 1, pageSize: 10 }
);
```

### 5. File Upload
```typescript
// Upload file với BaseService
const attachment = await chatService.uploadAttachment(file, {
  conversationId: 'conv123'
});
```

### 6. Count Messages
```typescript
// Đếm tin nhắn
const count = await chatService.countMessages({
  conversationId: 'conv123',
  read: false
});
```

## 🔧 Migration Guide

### 1. Cập nhật ChatPage Component

**Trước:**
```typescript
const result = await chatService.getMessages({
  query: conversationId,
  page: 1,
  pageSize: 100,
  populate: ['sender']
});
setMessages(result.rows);
```

**Sau:**
```typescript
const result = await chatService.getMessages(conversationId, {
  page: 1,
  pageSize: 100,
  populate: ['senderId']
});

// getMessages now returns PaginatedResult with 'rows' field (backend format)
setMessages(result.rows);
```

### 2. Cập nhật Error Handling

**Trước:**
```typescript
try {
  const message = await chatService.sendMessage(conversationId, text);
  setMessages(prev => [...prev, message]);
} catch (error) {
  // Manual error handling
  showErrorToast('Failed to send message');
}
```

**Sau:**
```typescript
const message = await chatService.sendMessage(conversationId, text);
if (message) {
  setMessages(prev => [...prev, message]);
}
// Error handling được xử lý tự động với toast
```

### 3. Backward Compatibility

Để tương thích với code cũ, có thể sử dụng method `getMessagesByParams`:

```typescript
// Legacy method - tương thích với code cũ
async getMessagesByParams(params: MessageSearchParams = {}): Promise<PaginatedResult<ChatMessage>> {
  const result = await this.getAllPaginate<ChatMessage>(
    API.MESSAGES,
    {
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      ...params.query
    },
    params.populate || ['senderId'],
    true,
    false
  );

  if (!result) {
    return {
      data: [],
      total: 0,
      page: 1,
      pageSize: 20,
      totalPages: 0
    };
  }

  // Convert PaginatedResponse to PaginatedResult (backend format)
  return {
    rows: result.data,  // Convert 'data' to 'rows' for backend compatibility
    total: result.pagination.total,
    page: result.pagination.page,
    pageSize: result.pagination.pageSize,
    totalPages: result.pagination.totalPages
  };
}
```

## 💡 Best Practices

1. **Luôn check null response:**
   ```typescript
   const result = await chatService.getMessages(conversationId);
   if (result) {
     // Process data
   }
   ```

2. **Sử dụng populate đúng cách:**
   ```typescript
   // Populate senderId thay vì sender
   const messages = await chatService.getMessages(conversationId, {
     populate: ['senderId']
   });
   ```

3. **Handle loading states:**
   ```typescript
   // Loading được handle tự động, nhưng có thể disable nếu cần
   const result = await chatService.getMessages(conversationId, {}, false); // No loading
   ```

4. **Sử dụng search cho performance:**
   ```typescript
   // Thay vì filter client-side, sử dụng server-side search
   const results = await chatService.searchMessages('keyword', conversationId);
   ```

## 🚨 Breaking Changes

1. **Method signature thay đổi:** `getMessages` cần `conversationId` parameter
2. **Response format giữ nguyên:** `PaginatedResult` với `rows` field (khớp với backend)
3. **Internal conversion:** BaseService `PaginatedResponse` được convert thành `PaginatedResult`
4. **Populate fields:** `sender` → `senderId`
5. **Error handling:** Methods không throw exception, return empty result thay vì null

## 🔄 Rollback Plan

Nếu cần rollback, có thể:

1. Sử dụng `getMessagesByParams` method cho backward compatibility
2. Tạo wrapper methods để convert response format
3. Giữ lại old methods với suffix `_legacy`

```typescript
// Rollback wrapper
async getMessages_legacy(params: MessageSearchParams = {}): Promise<PaginatedResult<ChatMessage>> {
  return this.getMessagesByParams(params);
}
```
