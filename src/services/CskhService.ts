import {cskh_api} from "@/lib/cskh.api.ts";
import {BaseService} from "@/services/BaseService.ts";
import {ChatMessage} from "@/services/ChatService.ts";

interface SendMessagePayload {
    text: string;
}

interface SendMessageResponse {
    success: boolean;
    data?: any;
}

interface paramsConversations {
    pagination?: boolean;
    page?: number;
    limit?: number;
    userId: string;
    title?: string;
}


const API = {
    CONVERSATIONS: '/conversations',
    // CONVERSATION_ID: '/conversations/:id',
    // MESSAGES: '/messages',
    // MESSAGE_ID: '/messages/:id',
    // MESSAGES_BY_CONVERSATION: '/messages/conversation/:conversationId',
    // MESSAGES_BY_CONVERSATION_ID: '/messages/:conversationId',
    // FILES_UPLOAD: '/files/upload'
};


class CskhService {

    async getConversations(params: paramsConversations): Promise<unknown> {
        return cskh_api.get(API.CONVERSATIONS, params);
    }
}

export const cskhService = new CskhService();